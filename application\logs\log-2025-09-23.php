<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

DEBUG - 2025-09-23 10:35:14 --> UTF-8 Support Enabled
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 10:35:14 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 10:35:14 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 10:35:14 --> CORS Origin received: http://localhost:8080
DEBUG - 2025-09-23 10:35:14 --> CORS: Origin found in whitelist: http://localhost:8080
DEBUG - 2025-09-23 10:35:14 --> CORS headers set for origin: http://localhost:8080
DEBUG - 2025-09-23 10:35:14 --> Total execution time: 0.0545
DEBUG - 2025-09-23 10:37:14 --> UTF-8 Support Enabled
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 10:37:14 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 10:37:14 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 10:37:14 --> CORS Origin received: http://localhost:8080
DEBUG - 2025-09-23 10:37:14 --> CORS: Origin found in whitelist: http://localhost:8080
DEBUG - 2025-09-23 10:37:14 --> CORS headers set for origin: http://localhost:8080
DEBUG - 2025-09-23 10:37:14 --> Total execution time: 0.0489
DEBUG - 2025-09-23 10:38:01 --> UTF-8 Support Enabled
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 10:38:01 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 10:38:01 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 10:38:01 --> CORS Origin received: http://localhost:8080
DEBUG - 2025-09-23 10:38:01 --> CORS: Origin found in whitelist: http://localhost:8080
DEBUG - 2025-09-23 10:38:01 --> CORS headers set for origin: http://localhost:8080
DEBUG - 2025-09-23 10:38:01 --> Total execution time: 0.0613
DEBUG - 2025-09-23 10:38:23 --> UTF-8 Support Enabled
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 10:38:23 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 10:38:23 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 10:38:23 --> CORS Origin received: http://localhost:8080
DEBUG - 2025-09-23 10:38:23 --> CORS: Origin found in whitelist: http://localhost:8080
DEBUG - 2025-09-23 10:38:23 --> CORS headers set for origin: http://localhost:8080
DEBUG - 2025-09-23 10:38:23 --> Total execution time: 0.0616
DEBUG - 2025-09-23 10:43:04 --> UTF-8 Support Enabled
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 10:43:04 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 10:43:04 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 10:43:04 --> CORS Origin received: http://localhost:8080
DEBUG - 2025-09-23 10:43:04 --> CORS: Origin found in whitelist: http://localhost:8080
DEBUG - 2025-09-23 10:43:04 --> CORS headers set for origin: http://localhost:8080
DEBUG - 2025-09-23 10:43:04 --> Total execution time: 0.0523
DEBUG - 2025-09-23 10:49:22 --> UTF-8 Support Enabled
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 10:49:22 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 10:49:22 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 10:49:22 --> CORS Origin received: http://localhost:8080
DEBUG - 2025-09-23 10:49:22 --> CORS: Origin found in whitelist: http://localhost:8080
DEBUG - 2025-09-23 10:49:22 --> CORS headers set for origin: http://localhost:8080
DEBUG - 2025-09-23 10:49:22 --> Total execution time: 0.0386
DEBUG - 2025-09-23 10:49:54 --> UTF-8 Support Enabled
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 10:49:54 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 10:49:54 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 10:49:54 --> CORS Origin received: http://localhost:8080
DEBUG - 2025-09-23 10:49:54 --> CORS: Origin found in whitelist: http://localhost:8080
DEBUG - 2025-09-23 10:49:54 --> CORS headers set for origin: http://localhost:8080
DEBUG - 2025-09-23 10:49:54 --> Total execution time: 0.0552
DEBUG - 2025-09-23 11:34:19 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:34:19 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:34:19 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:34:19 --> CORS Origin received: 
DEBUG - 2025-09-23 11:34:19 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:34:19 --> Total execution time: 0.0520
DEBUG - 2025-09-23 11:43:05 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:43:05 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:43:05 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:43:05 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:43:05 --> 404 Page Not Found: Api/v1
DEBUG - 2025-09-23 11:43:21 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:43:21 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:43:21 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:43:21 --> CORS Origin received: 
DEBUG - 2025-09-23 11:43:21 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:43:21 --> Total execution time: 0.0605
DEBUG - 2025-09-23 11:43:56 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:43:56 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:43:56 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:43:56 --> CORS Origin received: 
DEBUG - 2025-09-23 11:43:56 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:43:56 --> Total execution time: 0.0553
DEBUG - 2025-09-23 11:45:06 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:45:06 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:45:06 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:45:06 --> CORS Origin received: 
DEBUG - 2025-09-23 11:45:06 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:45:06 --> Total execution time: 0.0481
DEBUG - 2025-09-23 11:46:10 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:46:10 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:46:10 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:46:10 --> CORS Origin received: 
DEBUG - 2025-09-23 11:46:10 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:46:10 --> Total execution time: 0.0735
DEBUG - 2025-09-23 11:46:38 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:46:38 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:46:38 --> CORS Origin received: 
DEBUG - 2025-09-23 11:46:38 --> CORS headers set for origin: *
ERROR - 2025-09-23 11:46:38 --> Severity: 8192 --> str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated B:\xampp\htdocs\bajatravel\system\core\Output.php 457
DEBUG - 2025-09-23 11:46:38 --> Total execution time: 0.0608
DEBUG - 2025-09-23 11:55:35 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:55:35 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:55:35 --> CORS Origin received: 
DEBUG - 2025-09-23 11:55:35 --> CORS headers set for origin: *
ERROR - 2025-09-23 11:55:35 --> Severity: 8192 --> str_replace(): Passing null to parameter #3 ($subject) of type array|string is deprecated B:\xampp\htdocs\bajatravel\system\core\Output.php 457
DEBUG - 2025-09-23 11:55:35 --> Total execution time: 0.0376
DEBUG - 2025-09-23 11:55:41 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:55:41 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:55:41 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:55:41 --> CORS Origin received: 
DEBUG - 2025-09-23 11:55:41 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:55:41 --> Total execution time: 0.0599
DEBUG - 2025-09-23 11:56:02 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:56:02 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:56:02 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:56:02 --> CORS Origin received: 
DEBUG - 2025-09-23 11:56:02 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:56:02 --> Total execution time: 0.0770
DEBUG - 2025-09-23 11:56:15 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:56:15 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:56:15 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:56:15 --> CORS Origin received: 
DEBUG - 2025-09-23 11:56:15 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:56:15 --> Total execution time: 0.0742
DEBUG - 2025-09-23 11:56:30 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:56:30 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:56:30 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:56:30 --> CORS Origin received: 
DEBUG - 2025-09-23 11:56:30 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:56:30 --> Total execution time: 0.0619
DEBUG - 2025-09-23 11:56:45 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:56:45 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:56:45 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:56:45 --> CORS Origin received: 
DEBUG - 2025-09-23 11:56:45 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:56:45 --> Total execution time: 0.0530
DEBUG - 2025-09-23 11:57:47 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:57:47 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:57:47 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:57:47 --> CORS Origin received: 
DEBUG - 2025-09-23 11:57:47 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:57:47 --> Total execution time: 0.0647
DEBUG - 2025-09-23 11:58:02 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:58:02 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:58:02 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:58:02 --> CORS Origin received: 
DEBUG - 2025-09-23 11:58:02 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:58:02 --> Total execution time: 0.0521
DEBUG - 2025-09-23 11:58:45 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:58:45 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:58:45 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:58:45 --> CORS Origin received: 
DEBUG - 2025-09-23 11:58:45 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:58:45 --> Total execution time: 0.0501
DEBUG - 2025-09-23 11:59:02 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:59:02 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:59:02 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:59:02 --> CORS Origin received: 
DEBUG - 2025-09-23 11:59:02 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:59:02 --> Total execution time: 0.0499
DEBUG - 2025-09-23 11:59:23 --> UTF-8 Support Enabled
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 11:59:23 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 11:59:23 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 11:59:23 --> CORS Origin received: 
DEBUG - 2025-09-23 11:59:23 --> CORS headers set for origin: *
DEBUG - 2025-09-23 11:59:23 --> Total execution time: 0.0486
DEBUG - 2025-09-23 12:00:10 --> UTF-8 Support Enabled
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 12:00:10 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 12:00:10 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 12:00:10 --> CORS Origin received: 
DEBUG - 2025-09-23 12:00:10 --> CORS headers set for origin: *
DEBUG - 2025-09-23 12:00:10 --> Total execution time: 0.0824
DEBUG - 2025-09-23 12:01:28 --> UTF-8 Support Enabled
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 12:01:28 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 12:01:28 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 12:01:28 --> CORS Origin received: 
DEBUG - 2025-09-23 12:01:28 --> CORS headers set for origin: *
DEBUG - 2025-09-23 12:01:28 --> Total execution time: 0.0635
DEBUG - 2025-09-23 12:08:26 --> UTF-8 Support Enabled
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property CI_URI::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\URI.php 101
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property CI_Router::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Router.php 127
DEBUG - 2025-09-23 12:08:26 --> Global POST, GET and COOKIE data sanitized
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$benchmark is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$hooks is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$config is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$log is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$utf8 is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$uri is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$exceptions is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$router is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$output is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$security is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$input is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$lang is deprecated B:\xampp\htdocs\bajatravel\system\core\Controller.php 82
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$db is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 396
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property CI_DB_mysqli_driver::$failover is deprecated B:\xampp\htdocs\bajatravel\system\database\DB_driver.php 371
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Return type of CI_Session_database_driver::open($save_path, $name) should either be compatible with SessionHandlerInterface::open(string $path, string $name): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 129
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Return type of CI_Session_database_driver::close() should either be compatible with SessionHandlerInterface::close(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 279
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Return type of CI_Session_database_driver::read($session_id) should either be compatible with SessionHandlerInterface::read(string $id): string|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 151
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Return type of CI_Session_database_driver::write($session_id, $session_data) should either be compatible with SessionHandlerInterface::write(string $id, string $data): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 207
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Return type of CI_Session_database_driver::destroy($session_id) should either be compatible with SessionHandlerInterface::destroy(string $id): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 296
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Return type of CI_Session_database_driver::gc($maxlifetime) should either be compatible with SessionHandlerInterface::gc(int $max_lifetime): int|false, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice B:\xampp\htdocs\bajatravel\system\libraries\Session\drivers\Session_database_driver.php 334
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$session is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$email is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$ftp is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 1283
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$Bajatravel_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
ERROR - 2025-09-23 12:08:26 --> Severity: 8192 --> Creation of dynamic property api::$api_model is deprecated B:\xampp\htdocs\bajatravel\system\core\Loader.php 358
DEBUG - 2025-09-23 12:08:26 --> CORS Origin received: 
DEBUG - 2025-09-23 12:08:26 --> CORS headers set for origin: *
ERROR - 2025-09-23 12:08:26 --> Email send error for booking BT2024001234: Email configuration not found
DEBUG - 2025-09-23 12:08:26 --> Total execution time: 0.0549
