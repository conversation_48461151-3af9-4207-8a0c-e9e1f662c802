<?php  
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------
| Transportation
| -------------------------------------------------------------------
| This file contains parameters for reservation engine
|
*/
$config['custom_book'] = array(
      /** 
            * Fleets with description to show in any view
            * -------------------------
            * An array with the id_code of fleet.
      */
      'fleets_id_codes' => ['BTT001','BTT002','BTT003','BTT004','BTT005'],

      'placeholder_location' => 'Airport, Hotel',
      'placeholder_location_es' => 'Aeropuerto, Hotel',
      'grocery_stop' => 30, // Public Price
      'golf_clubs_bags' => 20, // Public Price
      'surfboards' => 20, // Public Price

      /** 
            * Commission
            * -------------------------
            * Define admin_fee and tax.
      */
      'virtual_commissions' => false, // calcule commissions from subtotal, subtotal will be total_fare
      'tax' => 0, // porcent / if you want to avoid it, you must set this to 0
      'admin_fee' => 0, // porcent / if you want to avoid it, you must set this to 0

      /** 
            * Custom Email
            * -------------------------
            * Define email subjects and senders.
      */
      'sender_email' => '<EMAIL>',
      'sender_name' => 'BajaTravel Transport Reserve',
      'subject_prereserva' => 'PreReserva - BajaTravel',
      'subject_reservation' => 'BajaTravel Transport Reserve',
      'bcc_book' => '<EMAIL>', //<EMAIL>
      'bcc_book_payed' => '<EMAIL>',
      'bcc_book_payed_cabo_villas' => '<EMAIL>,<EMAIL>,<EMAIL>',
      'bcc_book_payed_acre' => '<EMAIL>,<EMAIL>'
);

$config['smtp_reservationsBTT'] = array(
      "protocol" => "smtp",
      "smtp_crypto" => "ssl",
      "charset" => "utf-8",
      "mailtype" => "html",
      "smtp_host" => "mail.bajatraveltransportation.com",
      "smtp_port" => 465,
      "smtp_user" => "<EMAIL>",
      "smtp_pass" => "BajaTT112#",
      "smtp_timeout" => 30,
      "crlf" => "\r\n",
      "newline" => "\r\n"
);

// Configuración Gmail para desarrollo (REEMPLAZA CON TUS CREDENCIALES)
$config['smtp_mailtrapSandbox'] = array(
      "protocol" => "smtp",
      "smtp_host" => "smtp.gmail.com",
      "smtp_port" => 587,
      "smtp_crypto" => "tls",
      "smtp_user" => "<EMAIL>", // CAMBIA ESTO
      "smtp_pass" => "TU_APP_PASSWORD", // CAMBIA ESTO
      "smtp_timeout" => 30,
      "charset" => "utf-8",
      "mailtype" => "html",
      "wordwrap" => TRUE,
      "wrapchars" => 76,
      "crlf" => "\r\n",
      "newline" => "\r\n"
);

// Configuración alternativa SMTP para desarrollo (comentada)
/*
$config['smtp_mailtrapSandbox'] = array(
      "protocol" => "smtp",
      "smtp_crypto" => "tls",
      "smtp_host" => "smtp-relay.brevo.com",
      "smtp_port" => 587,
      "smtp_user" => "<EMAIL>",
      "smtp_pass" => "bhXDcq1Mt2PxHUvw",
      "smtp_timeout" => 30,
      "wordwrap" => TRUE,
      "wrapchars" => 76,
      "crlf" => "\r\n",
      "newline" => "\r\n",
      "charset" => "utf-8",
      "mailtype" => "html"
);
*/
