<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Transportation extends CI_Controller {

	/**
	 * Index Page for this controller.
	 *
	 * Maps to the following URL
	 * 		http://example.com/index.php/welcome
	 *	- or -
	 * 		http://example.com/index.php/welcome/index
	 *	- or -
	 * Since this controller is set as the default controller in
	 * config/routes.php, it's displayed at http://example.com/
	 *
	 * So any other public methods not prefixed with an underscore will
	 * map to /index.php/welcome/<method_name>
	 * @see https://codeigniter.com/user_guide/general/urls.html
	 */

    function __construct()
    {
        parent::__construct();

        // Load Transportation library config
        $this->config->load('transportation');
    }

	public function booking_service ()
	{
        if ($this->uri->segment(2) == 'cabo-villas') {
            redirect('booking-service/corazon-cabo', 'refresh');
        }

        if ($this->uri->segment(2) == 'corazon-cabo') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Corazon Cabo Resort and SPA';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
            } else if ($this->uri->segment(2) == 'medano-hotel') {
                $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
                $passagerForm1['to'] = 'Medano Hotel and Suites';
                $passagerForm1['roundtrip'] = true;
                $passagerForm1['arrivalDate'] = false;
                $passagerForm1['departureDate'] = false;
                $passagerForm1['adults'] = 1;
                $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'acre-treehouses') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'ACRE Treehouses';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'montemar') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Montemar Luxury Residences';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'villa-nautilus') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Villa Nautilus';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
            } else if ($this->uri->segment(2) == 'libertad-cabo') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Libertad Cabo';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'amaterra') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Amaterra Residences';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'bahia-tezal') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Bahia del Tezal';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'vista-velas') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Vista Velas I, II, III';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'tramonti') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Tramonti';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'villa-paloma') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Villa Paloma';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'villa-quetzal') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Villa Quetzal';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'villa-paradise') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Villa Paradise';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else if ($this->uri->segment(2) == 'cabo-peninsula') {
            $passagerForm1['from'] = 'Los Cabos International Airport (SJD)';
            $passagerForm1['to'] = 'Cabo Peninsula Residences';
            $passagerForm1['roundtrip'] = true;
            $passagerForm1['arrivalDate'] = false;
            $passagerForm1['departureDate'] = false;
            $passagerForm1['adults'] = 1;
            $passagerForm1['kids'] = 0;

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $this->session->set_userdata('transaction_id', $transaction_id);
            } else {
                $transaction_id = $this->session->userdata('transaction_id');
            }

            $passagerForm1['transaction_id'] = $transaction_id;
            $datos['valuesFromIndex'] = true;
            $datos['passagerForm1'] = $passagerForm1;
            $datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
            $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        } else {
            // Datos recibidos de la página index
            $passagerForm1 = $this->session->userdata('transp_ret');

            $datos['passagerForm1'] = $passagerForm1;
    		$datos['contenido'] = 'booking-service';
            $datos['js__reservation'] = 'yes';
    		$this->load->view('templates/front-end/template-1st-en-transportation', $datos);
        }
	}

	public function booking_details ()
	{
        $transp_ret = $this->session->userdata('transp_ret');
        if ($transp_ret) {
            if (isset($transp_ret['fleet']) && $this->session->flashdata('valuesFromIndex')) {
                // $access_token = $this->session->userdata('access_token');

                // if (empty($access_token)) {
                //     $this->generateTokenPPP();
                // }
                
                $datos['contenido'] = 'booking-details';
                $datos['js__booking_details'] = 'yes';
                $datos['transp_ret'] = $this->session->userdata('transp_ret');
                $this->load->view('templates/front-end/template-1st-en-transportation', $datos); 
            } else {
                redirect('booking-service');
            }
        } else {
            redirect('booking-service');
        }
	}

	public function booking_confirmation ()
	{
        $this->load->model('Transportation_model');

        if ($this->session->userdata('transp_ret')) {
            $custom_book = $this->config->item('custom_book');
            $emailConfig = ENVIRONMENT === 'production' ? $this->config->item('smtp_reservationsBTT') : $this->config->item('smtp_mailtrapSandbox');
            // $this->load->library('email');
            $datos['js__booking_details'] = 'yes';
    		$datos['contenido'] = 'booking-confirmation';
            $datos['transp_ret'] = $this->session->userdata('transp_ret');

            $subject = $datos['transp_ret']['firstName'].' '.$datos['transp_ret']['lastName'].' - Baja Travel New Transportation Booking';
            $msg = $this->load->view('templates/front-end/en/mail/booking-confirmation',$datos,true);
            
            $this->email->initialize($emailConfig);
            // $this->email->set_newline("\r\n");
            
            $this->email->from('<EMAIL>', 'Baja Travel');
            $this->email->to((ENVIRONMENT === 'production') ? $datos['transp_ret']['email'] : '<EMAIL>');

            if ($datos['transp_ret']['to'] == 'Corazon Cabo Resort and SPA') {
                $this->email->bcc((ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed_cabo_villas'] : '<EMAIL>');
                $this->email->attach('https://bajatraveltransportation.com/assets/procedures/custom-procedures.pdf');
            } elseif ($datos['transp_ret']['to'] == 'Medano Hotel and Suites') {
                $this->email->bcc((ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed'] : '<EMAIL>');
                $this->email->attach('https://bajatraveltransportation.com/assets/procedures/custom-procedures-medano.pdf');
            } elseif ($datos['transp_ret']['to'] == 'ACRE Treehouses') {
                $this->email->bcc((ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed_acre'] : '<EMAIL>');
                $this->email->attach('https://bajatraveltransportation.com/assets/procedures/custom-procedures-acre.pdf');
            } else {
                $this->email->bcc((ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed'] : '<EMAIL>');
                $this->email->attach('https://bajatraveltransportation.com/assets/procedures/custom-procedures-btt.pdf');
            }
            $this->email->subject($subject);
            $this->email->message($msg);

            //Save Retail in DataBase
            if ($this->Transportation_model->check_retail_booking($datos['transp_ret']['transaction_id'])) {
                $this->Transportation_model->update_trans_booking($datos['transp_ret']['transaction_id'], $datos['transp_ret']);
            } else {
                $this->Transportation_model->save_trans_booking($datos['transp_ret']);
            }

            // Send the e-mail and manage errors
            try {
                if ($this->email->send()) {
                    $this->session->sess_destroy();
                    $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
                } else {
                    log_message('debug', 'Something wrong happened while sending your message: email->send() != true');
                    $this->session->sess_destroy();
                    $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
                }
            } catch (Exception $e) {
                log_message('debug', "Something wrong happened while sending your message (".$e->getMessage().")");
                $this->session->sess_destroy();
                $this->load->view('templates/front-end/template-1st-en-transportation', $datos);
            }
        } else {
            redirect('home');
        }
	}

    //Guardar la información en un paso anterior para no perder datos, payment_method Pending
    public function save_previous_data ()
    {
        $_POST = json_decode(file_get_contents('php://input'), true);
        $this->load->model('Transportation_model');

        if ($this->session->userdata('transp_ret')) {

            $trans_retail_obj = $_POST['trans_retail_obj'];
            // $url = base_url($_POST['url']);
            $this->session->set_userdata('payment_method', 'pending');

            $arrivalDate = date_create_from_format('Y-m-d', $trans_retail_obj['arrivalDate']);
            $trans_retail_obj['arrivalDateFormattted'] = date_format($arrivalDate, 'M d, Y');

            if (isset($trans_retail_obj['departureDate'])) {
                $departureDate = date_create_from_format('Y-m-d', $trans_retail_obj['departureDate']);
                $trans_retail_obj['departureDateFormattted'] = date_format($departureDate, 'M d, Y');
            }

            if ($trans_retail_obj['roundtrip'] && $trans_retail_obj['fleet']['service'] == 'S' && $trans_retail_obj['fromdep'] == 'Corazon Cabo Resort and SPA') {
                $trans_retail_obj['fleet']['imgurl'] = 'van-shuttle.png';
            }

            if ($this->session->userdata('transaction_id') == null) {
                $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                $trans_retail_obj['transaction_id'] = $transaction_id;
                $this->session->set_userdata('transaction_id', $transaction_id);
            }

            //Save Retail in DataBase
            if ($this->Transportation_model->check_retail_booking($trans_retail_obj['transaction_id'])) {
                $this->Transportation_model->update_trans_booking($trans_retail_obj['transaction_id'], $trans_retail_obj);
            } else {
                $this->Transportation_model->save_trans_booking($trans_retail_obj);
            }
            
            // $host = ENVIRONMENT === 'production' ? 'https://api.paypal.com' : 'https://api.sandbox.paypal.com';
            // $websiteUrl = ENVIRONMENT === 'production' ? 'https://bajatraveltransportation.com' : 'http://localhost:8082/bajatravel-transportation';
            // $paypalMode = ENVIRONMENT === 'production' ? 'live' : 'sandbox';
            #CREATE PAYMENT
            // $url = $host.'/v1/payments/payment';
            /* $items = array();

            foreach ($trans_retail_obj['items'] as $item) {
                $data = array(
                    "name" => $item['name'],
                    "description" => $item['description'],
                    "quantity" => $item['quantity'],
                    "price" => number_format($item['unit_amount']['value'],2,'.',''),
                    "currency" => $item['unit_amount']['currency_code']
                );
                array_push($items,$data);
            } */
            
            /* $payment = '{
                "intent":"sale",
                "redirect_urls":{
                    "return_url":"https://www.paypal.com",
                    "cancel_url":"https://www.paypal.com"
                },
                "application_context": {
                    "shipping_preference": "NO_SHIPPING"
                },
                "payer":{
                    "payment_method":"paypal"
                },
                "transactions":[
                    {
                        "amount":{
                            "total": "'.number_format($trans_retail_obj['subtotal'],2,'.','').'",
                            "currency": "USD"
                        },
                        "description": "BajaTravel Transportation Service",
                        "custom": "'.$trans_retail_obj['transaction_id'].'",
                        "payment_options": {
                            "allowed_payment_method": "IMMEDIATE_PAY"
                        }
                    }
                ]
            }'; */
            /* "transactions":[
                    {
                        "item_list": {
                            "items": '.json_encode($items).'
                        } 
                    }
                ]
            */

            // $access_token = $this->session->userdata('access_token');
            // $json_resp = $this->make_post_call($url, $payment, $access_token);

            #Get the approval URL for later use
            // $approval_url = $json_resp['links']['1']['href'];
            // $executePayment_url = $json_resp['links']['2']['href'];
            
            #Get the token out of the approval URL
            // $token = substr($approval_url,-20);
            
            #Get the PaymentID for later use
            // $paymentID = ($json_resp['id']);
            
            #Put JSON in a nice readable format
            // $json_resp = stripslashes($this->json_format($json_resp));            

            // $jsonResponse['url'] = $url;
            // $jsonResponse['approval_url'] = $approval_url;
            // $jsonResponse['executePayment_url'] = $executePayment_url;
            // $jsonResponse['paymentID'] = $paymentID;
            // $jsonResponse['paypalMode'] = $paypalMode;
            // $this->output->set_content_type('application/json');
            // $this->output->set_output(json_encode($jsonResponse));
        }
    }

    public function getData ()
    {
        $airportDB = $this->Bajatravel_model->all_airports();
        // Converting an array/stdClass -> array
        $airportDB = json_decode(json_encode($airportDB), true);
        $airlineDB = $this->Bajatravel_model->all_airlines();
        $hotelDB = $this->Bajatravel_model->all_hotels();
        $hotelDB = json_decode(json_encode($hotelDB), true);
        $locations = array();
        $airportLocations = array();
        // $locations_stops = array();
        $passagerForm1 = $this->session->userdata('transp_ret');

        foreach ($airportDB as $item) {
            $airportLocations[] = array(
                'name' => $item['name'],
                'nombre' => $item['nombre'],
                'address' => '',
                'city' => '',
                'type' => 'Airport',
                'tipo' => 'Aeropuerto'
            );
        }

        foreach ($hotelDB as $item) {
            $locations[] = array(
                'name' => $item['nombre'],
                'nombre' => $item['nombre'],
                'address' => $item['address'],
                'city' => $item['city'],
                'type' => 'Hotel',
                'tipo' => 'Hotel'
            );

            /*if ($custom_book['fetch_hotels_stops']) {
                $locations_stops[] = $location;
            }*/
        }

        $jsonResponse['locations'] = $locations;
        $jsonResponse['airportLocations'] = $airportLocations;
        $jsonResponse['passagerForm1'] = $passagerForm1;
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($jsonResponse));
    }

    public function getAllHotels()
    {
        $hotelDB = $this->Bajatravel_model->all_hotels();
        $hotelDB = json_decode(json_encode($hotelDB), true);
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($hotelDB));
    }

    public function sharedRates()
    {
        $rates = $this->Bajatravel_model->shared_rates();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($rates));
    }

    public function getFleetServices() {
        $fleets = $this->Bajatravel_model->all_service_fleets();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($fleets));
    }

    public function getBasicData()
    {
        $airportDB = $this->Bajatravel_model->all_airports();
        $airlineDB = $this->Bajatravel_model->all_airlines();
        $hotelDB = $this->Bajatravel_model->all_hotels();
        $fleets = $this->Bajatravel_model->all_service_fleets();
        $fleets_disabled = $this->Bajatravel_model->get_fleets_disabled();
        $rates = $this->Bajatravel_model->all_rates();
        $zones = $this->Bajatravel_model->get_zones();

        $options_airline = array();
        $locations = array();
        $airportLocations = array();
        $custom_book = $this->config->item('custom_book');

        // Converting an array/stdClass -> array
        $airportDB = json_decode(json_encode($airportDB), true);
        foreach ($airportDB as $item) {
            $airportLocations[] = array(
                'name' => $item['name'],
                'nombre' => $item['nombre'],
                'address' => '',
                'city' => '',
                'type' => 'Airport',
                'tipo' => 'Aeropuerto'
            );
        }

        $airlineDB = json_decode(json_encode($airlineDB), true);
        foreach ($airlineDB as $item) {
            $options_airline[] = array(
                "name" => $item['code_airline'].' - '.$item['name'],
                "nombre" => $item['code_airline'].' - '.$item['name']
            );
        }

        $hotelDB = json_decode(json_encode($hotelDB), true);
        foreach ($hotelDB as $item) {
            $location = array(
                'name' => $item['nombre'],
                'nombre' => $item['nombre'],
                'address' => $item['address'],
                'city' => $item['city'],
                'type' => 'Hotel',
                'tipo' => 'Hotel'
            );
            $locations[] = $location;
        }

        // recorrer array con todos los fleets permitiendo la modificacion del array en cada iteracion
        foreach ($fleets as &$fleet) {
            // añadir los isguientes atributos a cada uno de los vehiculos
            $fleet->numcar = 1;
            $fleet->hours = 0;
            $fleet->passengers = intval($fleet->passengers);
            $fleet->maxpeople = intval($fleet->passengers);
            $fleet->visible = false;

            if (isset($fleet->vehicles)) {
                $fleet->vehicles = explode(',', $fleet->vehicles);
            }

            foreach ($rates as $rate) {
                if ($fleet->id_fleet == $rate->service_fleets_id_fleet && $rate->zones_idzones == 1 && stripos($rate->codeS, 'RT') !== false) {
                    if (floatval($rate->custom) > 0) {
                        // $fleet->rate = number_format($rate->custom, 2, '.', '');
                        $fleet->rate = number_format($rate->custom, 0, '.', '');
                    } else {
                        // $fleet->rate = number_format($rate->zone1, 2, '.', '');
                        $fleet->rate = number_format($rate->price, 0, '.', '');
                    }
                }
                /*if ($fleet->id_fleet == $rate->service_fleets_id_fleet) {
                   $fleet->activo = $rate->activo;
                }*/
            }
        }
        unset($fleet);

        $fleets = json_decode(json_encode($fleets), true);
        $fleetsDBFiltered = array();
        foreach ($fleets as $item) {
            $fleetsDBFiltered[] = $item;
        }

        // Get all zones
        $zones = json_decode(json_encode($zones), true);
        $zonesDBFiltered = array();
        foreach ($zones as $item) {
            $zonesDBFiltered[] = $item;
        }

        $fleets_description = array();
        foreach ($custom_book['fleets_id_codes'] as $id_code) {
            $fleet_data = $this->Bajatravel_model->get_Fleet_By_Id_Code($id_code);
            array_push($fleets_description, $fleet_data);
        }

        $fleets_disabled_list = array();

        if (is_array($fleets_disabled)) {
            foreach ($fleets_disabled as $item) {
                $zone = $this->Bajatravel_model->get_zone_by_id($item->zones_idzones);
                $hotel = $this->Bajatravel_model->get_Hotel_Name($item->hoteles_id_hotel);
                $fleet = $this->Bajatravel_model->get_data_fleet($item->service_fleets_id_fleet);
                $fleet = $fleet[0];

                $data = array(
                    'fleet' => $fleet->name,
                    'disable_one_way' => $item->disable_one_way,
                    'disable_round_trip' => $item->disable_round_trip,
                    'zone' => $zone,
                    'hotel' => $hotel
                );

                array_push($fleets_disabled_list, $data);
            }
            $this->session->set_userdata('fleets_disabled_list', $fleets_disabled_list);
        }

        $jsonResponse = array(
            'placeholder_location' => $custom_book['placeholder_location'],
            'locations' => $locations,
            'airportLocations' => $airportLocations,
            'options_airline' => $options_airline,
            'fleets' => $fleetsDBFiltered,
            'fleets_description' => $fleets_description,
            'zones' => $zonesDBFiltered,
            'rates' => $rates,
            'virtual_commissions' => $custom_book['virtual_commissions'],
            'tax_porcent' => $custom_book['tax'] / 100,
            'admin_fee_porcent' => $custom_book['admin_fee'] / 100,
            'grocery_stop' => $custom_book['grocery_stop'],
            'golf_clubs_bags' => $custom_book['golf_clubs_bags'],
            'surfboards' => $custom_book['surfboards']
        );
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($jsonResponse));
    }

    public function getHotelsZones()
    {
        $_POST = json_decode(file_get_contents('php://input'), true);
        if (empty($_POST)) {
            // show_404();
            $this->output->set_content_type('application/json');
            $this->output->set_status_header(400);
            // echo json_encode(array('error' => 'No data provided'));
            // or
            // echo '{"error": "No data provided"}';
            $this->output->set_output(json_encode(array('error' => 'No data provided')));
            // or
            // $this->output->set_output('{"error": "No data provided"}');
            // or
            // $this->output->set_output('No data provided');
            // or
            // echo json_encode(array('error' => 'No data provided'));
            return;
        } else {
            $zones = $this->Bajatravel_model->get_zones();
            $hotelDB = $this->Bajatravel_model->all_hotels();
            $jsonArray = array(
                'zones' => $zones,
                'hotelDB' => $hotelDB
            );
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($jsonArray));
        }
    }

    // SL = Start Location
    // EL = End Location
    public function getZone()
    {
        $_POST = json_decode(file_get_contents('php://input'), true);
        $roundtrip = $_POST['roundtrip'];
        $fieldset = $_POST['fieldset'];
        $name_location = $_POST['name_location'];
        $idzone = 0;
        switch ($_POST['type_Location']) {
            // case 'Community': $idzone = $this->Bajatravel_model->get_Community_Zone($name_location); break;
            case 'Airport': $idzone = $this->Bajatravel_model->get_Airport_Zone($name_location); break;
            // case 'Landmark': $idzone = $this->Bajatravel_model->get_Landmark_Zone($name_location); break;
            case 'Hotel': $idzone = $this->Bajatravel_model->get_Hotel_Zone($name_location); break;
        }

        $fleets_disabled_list = $this->session->userdata('fleets_disabled_list');
        $fleetsDisabled = array();

        if ($idzone > 0) {
            $zone = $this->Bajatravel_model->get_zone_by_id($idzone);

            if ($fieldset == 'EL' && $_POST['type_Location'] == 'Hotel') {
                $fleetsDisabled = $this->getFleetsDisabled($roundtrip, $zone, $name_location);
            }

            $jsonResponse = array('zone' => $zone, 'fleetsDisabled' => $fleetsDisabled);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($jsonResponse, JSON_NUMERIC_CHECK));
        }
    }

    public function getFleetsDisabled($roundtrip = false, $zone = 0, $hotel = '')
    {
        $fleets_disabled_list = $this->session->userdata('fleets_disabled_list');
        $fleetsDisabled = array();

        if (is_array($fleets_disabled_list)) {
            foreach ($fleets_disabled_list  as $item) {

                if ($item['disable_one_way'] || $item['disable_round_trip']) {
                    if ($item['disable_one_way'] && !$roundtrip) {
                        if ($item['zone'] == $zone) {
                            if ($item['hotel'] == $hotel) {
                                array_push($fleetsDisabled, $item['fleet']);
                            }
                        } else if ($item['hotel'] == $hotel) {
                            array_push($fleetsDisabled, $item['fleet']);
                        }
                    } else if ($item['disable_round_trip'] && $roundtrip) {
                        if ($item['zone'] == $zone) {
                            if ($item['hotel'] == $hotel) {
                                array_push($fleetsDisabled, $item['fleet']);
                            }
                        } else if ($item['hotel'] == $hotel) {
                            array_push($fleetsDisabled, $item['fleet']);
                        }
                    }
                } else {
                    if ($item['zone'] == $zone) {
                        if ($item['hotel'] == $hotel) {
                            array_push($fleetsDisabled, $item['fleet']);
                        }
                    } else if ($item['hotel'] == $hotel) {
                        array_push($fleetsDisabled, $item['fleet']);
                    }
                }
            }
        }

        return array_unique($fleetsDisabled);
    }

    public function initBooking()
    {
        if ($this->input->is_ajax_request()) {
            $formindex['from'] = $_POST['from'];
            $formindex['to'] = $_POST['to'];
            $formindex['roundtrip'] = isset($_POST['roundtrip']) ? true : false;
            $formindex['arrivalDate'] = $_POST['arrivalDate'];
            $formindex['departureDate'] = isset($_POST['departureDate']) ? $_POST['departureDate'] : false;
            $formindex['adults'] = $_POST['adults'];
            $formindex['kids'] = $_POST['kids'];

            if (!empty($formindex['from']) && !empty($formindex['to']) && !empty($formindex['arrivalDate']) && !empty($formindex['adults'])) {
                if ($this->session->userdata('transaction_id') == null) {
                    $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                    $this->session->set_userdata('transaction_id', $transaction_id);
                } else {
                    $transaction_id = $this->session->userdata('transaction_id');
                }

                $formindex['transaction_id'] = $transaction_id;
                $datos['passagerForm1'] = $formindex;

                $this->session->set_flashdata('valuesFromIndex', true);
                $this->session->set_userdata('transp_ret', $datos['passagerForm1']);

                $jsonResponse['url'] = base_url('booking-service');
                $jsonResponse['datos'] = $datos;
                $this->output->set_content_type('application/json');
                $this->output->set_output(json_encode($jsonResponse));
            } else {
                $jsonResponse['error_message'] = 'Please complete the form correctly.';
                $this->output->set_content_type('application/json');
                $this->output->set_output(json_encode($jsonResponse));
            }
        } else {
            show_404();
        }
    }

    public function random_str($length, $keyspace = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')
    {
        $pieces = [];
        $max = mb_strlen($keyspace, '8bit') - 1;
        for ($i = 0; $i < $length; ++$i) {
            $pieces []= $keyspace[mt_rand(0, $max)];
        }
        return implode('', $pieces);
    }

    public function set_trans_retail_obj() {
        $_POST = json_decode(file_get_contents('php://input'), true);

        if (!empty($_POST['trans_retail_obj']) && !empty($_POST['url'])) {
            $trans_retail_obj = $_POST['trans_retail_obj'];
            $currentDate = date('Y-m-d', strtotime("+1 day"));
            if ($trans_retail_obj['arrivalDate'] > $currentDate) {
                $arrivalDate = date_create_from_format('Y-m-d', $trans_retail_obj['arrivalDate']);
                $trans_retail_obj['arrivalDateFormattted'] = date_format($arrivalDate, 'M d, Y');
                $url = base_url($_POST['url']);
    
                if (!empty($trans_retail_obj['departureDate'])) {
                    if ($trans_retail_obj['departureDate'] >= $trans_retail_obj['arrivalDate']) {
                        $departureDate = date_create_from_format('Y-m-d', $trans_retail_obj['departureDate']);
                        $trans_retail_obj['departureDateFormattted'] = date_format($departureDate, 'M d, Y');
                        $trans_retail_obj['departureDayFormattted'] = date_format($departureDate, 'D');
                    } else {
                        $jsonResponse['error_message'] = 'Departure Date should be equal or greater than Arrival Date';
                        $this->output->set_content_type('application/json');
                        $this->output->set_output(json_encode($jsonResponse));            
                    }
                }
    
                if ($_POST['url'] == 'booking-confirmation' && $trans_retail_obj['roundtrip'] && $trans_retail_obj['fleet']['service'] == 'S' && $trans_retail_obj['fromdep'] == 'Corazon Cabo Resort and SPA') {
                    $trans_retail_obj['fleet']['imgurl'] = 'van-shuttle.png';
                }
    
                if ($this->session->userdata('transaction_id') == null) {
                    $transaction_id =  date("Y") . date("m") . $this->random_str(6, '0123456789');
                    $trans_retail_obj['transaction_id'] = $transaction_id;
                    $this->session->set_userdata('transaction_id', $transaction_id);
                }
    
                if (!empty($_POST['payment_method']) && !empty($_POST['authorization_code'])) {
                    if ($_POST['payment_method'] == 'PayPal' && $_POST['url'] == 'booking-confirmation') {
                        $trans_retail_obj['authorization_code'] = $_POST['authorization_code'];
                    }
                }
    
                if ($_POST['url'] == 'booking-details') {
                    $this->session->set_flashdata('valuesFromIndex', true);
                }
                
                $jsonResponse['url'] = $url;
                $this->session->set_userdata('transp_ret', $trans_retail_obj);
            } else {
                $jsonResponse['error_message'] = 'Arrival Date must be greater than 48 hours of current date';
            }
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($jsonResponse));
        }
    }

    public function payWithPayPalPlus($url, $execute)
    {
        $execute = json_encode($execute);
        $access_token = $this->session->userdata('access_token');
        return $this->make_post_call($url, $execute, $access_token);
    }

    public function generateTokenPPP()
    {
        $host = ENVIRONMENT === 'production' ? 'https://api.paypal.com' : 'https://api.sandbox.paypal.com';
        #GET ACCESS TOKEN
        
        $url = $host.'/v1/oauth2/token'; 
        $postArgs = 'grant_type=client_credentials';
        $this->get_access_token($url,$postArgs);
        #IMPORTANT - Please implement a process to generate a new access token once every 6 hours
        #You must avoid generating a new access token for every transaction/API Call
        #Failing to do so could result in PayPal blocking your account without previous notice.
    }
    
    #This function gets the OAuth2 Access Token which will be valid for 28800 seconds
    public function get_access_token($url, $postdata) {
        $client_id = ENVIRONMENT === 'production' ? '********************************************************************************' : '********************************************************************************';
        $secret_key = ENVIRONMENT === 'production' ? '********************************************************************************' : '********************************************************************************';
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_POST, true); 
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_CIPHER_LIST,'TLSv1');
        curl_setopt($curl, CURLOPT_USERPWD, $client_id . ":" . $secret_key);
        curl_setopt($curl, CURLOPT_HEADER, false); 
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); 
        curl_setopt($curl, CURLOPT_POSTFIELDS, $postdata); 
        #	curl_setopt($curl, CURLOPT_VERBOSE, TRUE);
        $response = curl_exec( $curl );
        if (empty($response)) {
            // some kind of an error happened
            die(curl_error($curl));
            curl_close($curl); // close cURL handler
        } else {
            $info = curl_getinfo($curl);
            curl_close($curl); // close cURL handler
            // Convert the result from JSON format to a PHP array 
            $jsonResponse = json_decode($response, TRUE);

            if($info['http_code'] != 200 && $info['http_code'] != 201 ) {
                if (isset($jsonResponse['error'])) {
                    if ($jsonResponse['error'] == 'invalid_token') {
                        $this->generateTokenPPP();
                    }
                }
                log_message('error', "Received error: " . $info['http_code']);
                log_message('error', "Raw response: " . print_r($jsonResponse,true));
            }
        }

        if (isset($jsonResponse['access_token'])) {
            $this->session->set_userdata('access_token', $jsonResponse['access_token']);
        }
    }

    #This function makes POST calls
    public function make_post_call($url, $postdata, $access_token) {
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_SSL_CIPHER_LIST,'TLSv1');
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            'Authorization: Bearer '.$access_token,
            'Accept: application/json',
            'Content-Type: application/json'
        ));

        curl_setopt($curl, CURLOPT_POSTFIELDS, $postdata); 
        #curl_setopt($curl, CURLOPT_VERBOSE, TRUE);
        $response = curl_exec( $curl );
        if (empty($response)) {
            // some kind of an error happened
            die(curl_error($curl));
            curl_close($curl); // close cURL handler
        } else {
            $info = curl_getinfo($curl);
            curl_close($curl); // close cURL handler
            // Convert the result from JSON format to a PHP array 
            $jsonResponse = json_decode($response, TRUE);

            if($info['http_code'] != 200 && $info['http_code'] != 201 ) {
                if (isset($jsonResponse['error'])) {
                    if ($jsonResponse['error'] == 'invalid_token') {
                        $this->generateTokenPPP();
                    }
                }
                log_message('error', "Received error: " . $info['http_code']);
                log_message('error', "Raw response: " . print_r($jsonResponse,true));
                $jsonResponse['http_code'] = $info['http_code'];
            } else {
                $jsonResponse['apiSuccess'] = true;
            }
        }

        return $jsonResponse;
    }

    public function json_format($json) {
        if (!is_string($json)) {
            if (phpversion() && phpversion() >= 5.4) {
                return json_encode($json, JSON_PRETTY_PRINT);
            }
            $json = json_encode($json);
        }

        $result      = '';
        $pos         = 0;               // indentation level
        $strLen      = strlen($json);
        $indentStr   = "\t";
        $newLine     = "\n";
        $prevChar    = '';
        $outOfQuotes = true;

        for ($i = 0; $i < $strLen; $i++) {
            // Speedup: copy blocks of input which don't matter re string detection and formatting.
            $copyLen = strcspn($json, $outOfQuotes ? " \t\r\n\",:[{}]" : "\\\"", $i);
            if ($copyLen >= 1) {
                $copyStr = substr($json, $i, $copyLen);
                // Also reset the tracker for escapes: we won't be hitting any right now
                // and the next round is the first time an 'escape' character can be seen again at the input.
                $prevChar = '';
                $result .= $copyStr;
                $i += $copyLen - 1;      // correct for the for(;;) loop
                continue;
            }
            
            // Grab the next character in the string
            $char = substr($json, $i, 1);
            
            // Are we inside a quoted string encountering an escape sequence?
            if (!$outOfQuotes && $prevChar === '\\') {
                // Add the escaped character to the result string and ignore it for the string enter/exit detection:
                $result .= $char;
                $prevChar = '';
                continue;
            }
            // Are we entering/exiting a quoted string?
            if ($char === '"' && $prevChar !== '\\') {
                $outOfQuotes = !$outOfQuotes;
            }
            // If this character is the end of an element,
            // output a new line and indent the next line
            else if ($outOfQuotes && ($char === '}' || $char === ']')) {
                $result .= $newLine;
                $pos--;
                for ($j = 0; $j < $pos; $j++) {
                    $result .= $indentStr;
                }
            }
            // eat all non-essential whitespace in the input as we do our own here and it would only mess up our process
            else if ($outOfQuotes && false !== strpos(" \t\r\n", $char)) {
                continue;
            }
            // Add the character to the result string
            $result .= $char;
            // always add a space after a field colon:
            if ($outOfQuotes && $char === ':') {
                $result .= ' ';
            }
            // If the last character was the beginning of an element,
            // output a new line and indent the next line
            else if ($outOfQuotes && ($char === ',' || $char === '{' || $char === '[')) {
                $result .= $newLine;
                if ($char === '{' || $char === '[') {
                    $pos++;
                }
                for ($j = 0; $j < $pos; $j++) {
                    $result .= $indentStr;
                }
            }
            $prevChar = $char;
        }
        return $result;
    }

}