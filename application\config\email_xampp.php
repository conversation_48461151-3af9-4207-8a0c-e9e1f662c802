<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------
| Email Configuration for XAMPP Development
| -------------------------------------------------------------------
| This file contains email configurations specifically for XAMPP
| development environment.
|
*/

// Gmail SMTP configuration (for development)
$config['gmail_smtp'] = array(
    'protocol' => 'smtp',
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_crypto' => 'tls',
    'smtp_user' => '', // Add your Gmail here
    'smtp_pass' => '', // Add your App Password here
    'smtp_timeout' => 30,
    'charset' => 'utf-8',
    'mailtype' => 'html',
    'wordwrap' => TRUE,
    'wrapchars' => 76,
    'crlf' => "\r\n",
    'newline' => "\r\n"
);

// Mailtrap configuration (for testing)
$config['mailtrap_smtp'] = array(
    'protocol' => 'smtp',
    'smtp_host' => 'sandbox.smtp.mailtrap.io',
    'smtp_port' => 2525,
    'smtp_crypto' => 'tls',
    'smtp_user' => '', // Add your Mailtrap username
    'smtp_pass' => '', // Add your Mailtrap password
    'smtp_timeout' => 30,
    'charset' => 'utf-8',
    'mailtype' => 'html',
    'wordwrap' => TRUE,
    'wrapchars' => 76,
    'crlf' => "\r\n",
    'newline' => "\r\n"
);

// Simple mail() function configuration (fallback)
$config['simple_mail'] = array(
    'protocol' => 'mail',
    'charset' => 'utf-8',
    'mailtype' => 'html',
    'wordwrap' => TRUE,
    'wrapchars' => 76,
    'crlf' => "\r\n",
    'newline' => "\r\n"
);
