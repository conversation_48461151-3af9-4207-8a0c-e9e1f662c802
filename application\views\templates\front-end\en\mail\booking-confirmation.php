<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Booking Confirmation</title>
  	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  	<meta name="viewport" content="width=device-width, initial-scale=1">
  	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
  	<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.0/css/all.css" integrity="sha384-lZN37f5QGtY3VHgisS14W3ExzMWZxybE1SJSEsQp9S+oqd12jhcu+A56Ebc1zFSJ" crossorigin="anonymous">
  <style type="text/css">
    /* CLIENT-SPECIFIC STYLES */
    @import url('https://fonts.googleapis.com/css?family=Nunito+Sans');
    body,
    table,
    td,
    a {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      font-family: 'Nunito Sans', sans-serif;
    }
    table,
    td {
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }
    img {
      -ms-interpolation-mode: bicubic;
    }
    
    /* RESET STYLES */
    img {
      border: 0;
      height: auto;
      line-height: 100%;
      outline: none;
      text-decoration: none;
    }
    table {
      border-collapse: collapse !important;
    }
    body {
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      width: 100% !important;
    }
    
    /* iOS BLUE LINKS */
    a[x-apple-data-detectors] {
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
    }
    
    /* MEDIA QUERIES */
    @media screen and (max-width: 480px) {
      .mobile-hide {
        display: none !important;
      }
      .mobile-center {
        text-align: center !important;
      }
      .align-center {
        max-width: initial !important;
      }
      h1 {
        display: inline-block;
        margin-right: auto !important;
        margin-left: auto !important;
      }
    }
    @media screen and (min-width: 480px) {
      .mw-50 {
        max-width: 50%;
      }
    }
    /* ANDROID CENTER FIX */
    div[style*="margin: 16px 0;"] {
      margin: 0 !important;
    }
    :root {
      --purple: #5a3aa5;
      --pink: #b91bab;
      --blue: #2cbaef;
      --green: #23c467;
    }
	.wrapper-thankyou-page {
		width: 100%;
		height: 100px;
	}
	.logo-thankyou-page {
		height: 120px;
		width: 120px;
		border-radius: 100px;
		position: relative;
		top: -60px;
		background-color: #fff;
	}
	.logo-thankyou-page img {
		margin-top: 25px;
		margin-left: 15px;
		width: 90px;
	}
	.wrapper-booking-confirmation {
		border-radius: 20px;
	}
  </style>
</head>
<body style="margin: 0 !important; padding: 0 !important; background-color: #fff;" bgcolor='#fff' bgcolor="#ffffff">
	<table width="100%" style="margin-top: 10px;">
		<tr><td align="center" style="background-color: #fff;">
			<table width="600px">
				<tr style='color: #fff;'>
					<td>
				        <div style="background-image: url(<?php echo base_url('assets/img/mail/email-header.jpg') ?>); height: 160px;"></div>
				    </td>
				</tr>
			</table>
			<table style='margin-top: -25px;'>
				<tr>
					<td>
						<div class='wrapper-booking-confirmation' style='background-color: #fff; margin-left: 15px; width: 570px;'>
							<table width="600">
								<tr><td colspan="2" style="text-align: center;"><h2 style="width: 570px; margin: 20px 0 10px 0; font-weight: bold;">Thanks for choosing us!</h2></td></tr>
								<tr><td colspan='2' style='text-align: center;'><h2 style="width: 570px; margin: 0 0 10px 0; font-weight: bold; color: #31b143;">Your Transportation is confirmed.</h2></td></tr>
								<tr><td colspan='2' style='text-align: center;'><h3 style="width: 570px; margin: 0 0 40px 0; font-weight: bold;">Booking ID: <?php echo $transp_ret['transaction_id']; ?></h3></td></tr>
								<?php if (!$transp_ret['roundtrip']) { ?>
								<tr>
									<td align="center" colspan="2">
										<table style="font-size: 16px;">
											<tr><td align="center" style="padding: 0 40px 0 40px; text-align: center;">
												<div style="background-image: url(<?php echo base_url('assets/img/units/'.$transp_ret['fleet']['imgurl']); ?>); background-repeat: no-repeat; background-size: 100%; height: 200px; width: 300px;"></div>
											</td></tr>
										</table>
									</td>
								</tr>
								<?php } ?>
								<?php if ($transp_ret['roundtrip']) {
									if ($transp_ret['fleet']['service'] == 'S') {
										if ($transp_ret['fromdep'] == 'Corazon Cabo Resort and SPA') {
											$imgFleet = base_url('assets/img/units/van-shuttle.png');
										} else {
											$imgFleet = base_url('assets/img/units/'.$transp_ret['fleet']['imgurl']);
										}
									} else {
										$imgFleet = base_url('assets/img/units/'.$transp_ret['fleet']['imgurl']);
									}
								?>
								<tr>
									<td width="50%" style="padding-left: 20px;">
										<div style="background-image: url(<?php echo $imgFleet; ?>); height: 160px; background-size: 100%; background-repeat: no-repeat;">
										</div>
									</td>
									<td>
										<table width="100%" style="font-size: 16px;">
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Name: </strong><?php echo $transp_ret['firstName'].' '.$transp_ret['lastName']; ?></td></tr>
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Email: </strong><?php echo $transp_ret['email']; ?></td></tr>
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Phone: </strong><?php echo $transp_ret['phone_pax_full']; ?></td></tr>
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Payment Method: </strong><?php echo $transp_ret['payment_method']; ?></td></tr>
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Total (USD): </strong><?php echo number_format($transp_ret['subtotal'], 2, '.', ''); ?></td></tr>
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Type Service: </strong> <?php echo ($transp_ret['roundtrip'] ? 'Round Trip' : 'One Way'); ?></td></tr>
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Passengers: </strong> <?php echo $transp_ret['adults']; ?> Adults</td></tr>
											<?php if($transp_ret['kids'] != 0) { ?>
											<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Passengers: </strong> <?php echo $transp_ret['kids']; ?> Kids</td></tr>
											<?php } ?>
										</table>								
									</td>
								</tr>
								<?php } ?>
								<!-- CONFIGURACIÓN ONE WAY -->
									<?php if (!$transp_ret['roundtrip']) { ?>
									<tr><td colspan="2"><h4 style="width: 570px; text-align: center; margin: 30px 0 0 0; font-size: 16px;">Booking Details</h4></td></tr>
									<tr>
										<td colspan='1' style='padding-left: 30px;'><h4 style='margin: 20px 0 10px 0; text-align: left; font-size: 16px; color: #0865c0;'>Contact Information</h4></td>
										<td colspan='1'><h4 style='margin: 20px 0 10px 0; padding-left: 20px; padding-bottom: 5px; text-align: left; font-size: 16px; color: #0865c0;'>Summary</h4></td>
									</tr>
									<tr>
										<td>
											<table style="font-size: 16px;">
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Name: </strong><?php echo $transp_ret['firstName'].' '.$transp_ret['lastName']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Email: </strong><?php echo $transp_ret['email']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Phone: </strong><?php echo $transp_ret['mobilePhone']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Payment Method: </strong><?php echo $transp_ret['payment_method']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Total (USD): </strong><?php echo number_format($transp_ret['subtotal'], 2, '.', ''); ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Type Service: </strong> 
													<?php echo ($transp_ret['roundtrip'] ? 'Round Trip' : 'One Way'); ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Passengers: </strong> <?php echo $transp_ret['adults']; ?> Adults</td></tr>
												<?php if($transp_ret['kids'] != 0) { ?>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Passengers: </strong> <?php echo $transp_ret['kids']; ?> Kids</td></tr>
												<?php } ?>
											</table>							
										</td>
										<td>
											<table style="font-size: 16px;">
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Service: </strong><?php echo $transp_ret['fleet']['name']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>From: </strong> <?php echo $transp_ret['from']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>To: </strong> <?php echo $transp_ret['to']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Airline: </strong> <?php echo $transp_ret['arrivalAirline']; ?> </td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Flight Number: </strong> <?php echo $transp_ret['arrivalFlight']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong><?php echo $transp_ret['pickupString']; ?> Time: </strong> <?php echo $transp_ret['arrivalTime']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Date: </strong> <?php echo $transp_ret['arrivalDate']; ?></td></tr>
												<?php if ($transp_ret['fromsl'] != 'Airport') { ?>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Pickup Time: </strong> <?php echo $transp_ret['arrivalPickup']; ?></td></tr>
												<?php } ?>
											</table>							
										</td>
									</tr>
									<?php } ?>
								<!-- FIN CONFIGURACIÓN ONE WAY -->
								<!-- CONFIGURACIÓN ROUND TRIP -->
									<?php if ($transp_ret['roundtrip']) { ?>
									<tr><td colspan="2"><h4 style="width: 570px; text-align: center; margin: 30px 0 0 0; font-size: 16px;">Flight Information</h4></td></tr>
									<tr>
										<td colspan='1' style='padding-left: 30px;'><h4 style='margin: 20px 0 10px 0; text-align: left; font-size: 16px; color: #0865c0;'>For your Arrival</h4></td>
										<td colspan='1'><h4 style='margin: 20px 0 10px 0; padding-left: 20px; padding-bottom: 5px; text-align: left; font-size: 16px; color: #0865c0;'>For your Departure</h4></td>
									</tr>
									<tr>
										<td>
											<table style="font-size: 16px;">
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Service: </strong><?php echo $transp_ret['fleet']['name']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>From: </strong> <?php echo $transp_ret['from']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>To: </strong> <?php echo $transp_ret['to']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Airline: </strong> <?php echo $transp_ret['arrivalAirline']; ?> </td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Flight Number: </strong> <?php echo $transp_ret['arrivalFlight']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong><?php echo $transp_ret['pickupString']; ?> Time: </strong> <?php echo $transp_ret['arrivalTime']; ?></td></tr>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Date: </strong> <?php echo $transp_ret['arrivalDate']; ?></td></tr>
												<?php if ($transp_ret['fromsl'] != 'Airport') { ?>
												<tr><td style="padding-left: 30px; padding-bottom: 5px;"><strong>Pickup Time: </strong> <?php echo $transp_ret['arrivalPickup']; ?></td></tr>
												<?php } ?>
											</table>							
										</td>
										<td>
											<table style="font-size: 16px;">
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>From: </strong> <?php echo $transp_ret['fromdep']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>To: </strong> <?php echo $transp_ret['todep']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Airline: </strong> <?php echo $transp_ret['departureAirline']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Flight Number: </strong><?php echo $transp_ret['departureFlight']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong><?php echo $transp_ret['pickupRTString']; ?> Time: </strong> <?php echo $transp_ret['departureTime']; ?></td></tr>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Date: </strong> <?php echo $transp_ret['departureDate']; ?></td></tr>
												<?php if ($transp_ret['fromdsl'] != 'Airport') { ?>
												<tr><td style="padding-left: 20px; padding-bottom: 5px;"><strong>Pickup Time: </strong> <?php echo $transp_ret['departurePickup']; ?></td></tr>
												<?php } ?>
											</table>							
										</td>
									</tr>
									<?php } ?>
								<!-- FIN CONFIGURACIÓN ROUND TRIP -->
								<tr>
									<td colspan="2" style="padding-top: 15px;">
										<?php if(!empty($transp_ret['babySeats'])) { ?>
											<?php if (!empty($transp_ret['babySeats'])) { ?>
												<tr><td colspan="2" style="padding-left: 30px; padding-bottom: 5px; font-size: 16px;"><strong>Baby Seats: </strong> <?php echo $transp_ret['babySeats']; ?></td></tr>
											<?php } ?>
										<?php } ?>
										<?php if(isset($transp_ret['carSeats'])) { ?>
											<?php if (!empty($transp_ret['carSeats'])) { ?>
												<tr><td colspan="2" style="padding-left: 30px; padding-bottom: 5px; font-size: 16px;"><strong>Car Seats: </strong> <?php echo $transp_ret['carSeats']; ?></td></tr>
											<?php } ?>
										<?php } ?>
										<?php if(isset($transp_ret['boosterSeats'])) { ?>
											<?php if (!empty($transp_ret['boosterSeats'])) { ?>
												<tr><td colspan="2" style="padding-left: 30px; padding-bottom: 5px; font-size: 16px;"><strong>Booster Seats: </strong> <?php echo $transp_ret['boosterSeats']; ?></td></tr>
											<?php } ?>
										<?php } ?>
										<?php if(!empty($transp_ret['add_grocery_stop']) && isset($transp_ret['grocery_stop'])) { ?>
											<tr><td colspan="2" style="padding-left: 30px; padding-bottom: 5px; font-size: 16px;"><strong>Grocery Stop (1 x <?php echo number_format($transp_ret['grocery_stop'], 2, '.', ''); ?>): </strong> <?php echo number_format($transp_ret['grocery_stop'], 2, '.', ''); ?></td></tr>
										<?php } ?>
										<?php if(!empty($transp_ret['add_golf_clubs_bags']) && isset($transp_ret['golfClubsBags']) && isset($transp_ret['golf_clubs_bags'])) { ?>
											<tr><td colspan="2" style="padding-left: 30px; padding-bottom: 5px; font-size: 16px;"><strong>Golf Clubs Bags (<?php echo $transp_ret['golfClubsBags'].' x '.number_format($transp_ret['golf_clubs_bags'], 2, '.', ''); ?>): </strong> <?php echo number_format($transp_ret['golfClubsBags'] * $transp_ret['golf_clubs_bags'], 2, '.', ''); ?></td></tr>
										<?php } ?>
										<?php if(!empty($transp_ret['add_surfboards']) && isset($transp_ret['surfBoards']) && isset($transp_ret['surfboards'])) { ?>
											<tr><td colspan="2" style="padding-left: 30px; padding-bottom: 5px; font-size: 16px;"><strong>Surfboards (<?php echo $transp_ret['surfBoards'].' x '.number_format($transp_ret['surfboards'], 2, '.', ''); ?>): </strong> <?php echo number_format($transp_ret['surfBoards'] * $transp_ret['surfboards'], 2, '.', ''); ?></td></tr>
										<?php } ?>
									</td>
								</tr>
								<?php if(isset($transp_ret['specialInstructions'])) { ?>
									<?php if (!empty($transp_ret['specialInstructions'])) { ?>
										<tr><td colspan="2" style="padding-left: 30px; padding-top: 10px; padding-bottom: 5px; font-size: 16px;"><strong>Special Instructions: </strong> <?php echo $transp_ret['specialInstructions']; ?></td></tr>
									<?php } ?>
								<?php } ?>
								<tr><td colspan="2"><h4 style="width: 570px; text-align: center; margin: 30px 0 10px 0;">Terminal Exit Instructions Upon Arrival</h4></td></tr>
								<tr>
									<td colspan="2" style="width: 600px; padding-left: 20px; padding-right: 20px; font-size: 12px;">
										<p style="width: 530px; text-align: justify;">These instructions are for you to make an easier exit and avoid confusion. Once you claim your luggage and clear customs, proceed right after passing the sliding doors and exit the terminal. Drivers are not allowed to enter the terminal premises for security reasons but he will be waiting for you right outside. Your chauffeur will be holding a sign with Baja Travel Transportation logo.</p>
										<p style="width: 530px; text-align: justify;">Notes: Timeshare personnel may try to approach you and distract you from exiting the terminal and will also take valuable time from you by telling you they are the transportation company staff, please do not pay attention to their offers and proceed to exit the terminal (Our driver can only wait so much time for you to come outside, if getting involved with timeshare personnel takes your time and over pass the waiting time the chauffeur will leave without you and the transportation cost will be non refundable). In the event you require further transportation services, contact us and we will gladly help you with any transportation services you may require. Do not hesitate to contact us for any changes about your transportation services or if you have any questions. Cancellations are to be notified 24 hours prior to transportation service to avoid full charge penalties.</p>
									</td>
								</tr>
								<tr><td colspan='2'><h4 style='width: 570px; text-align: center; margin-top: 20px; margin-bottom: 0; font-size: 14px;'><span style='color:#0865c0; font-weight: bold;'>Baja Travel Team</span></h4></td></tr>
								<tr><td colspan='2'>
									<h4 style='width: 570px; margin-top: 20px; text-align: center;'>If you have any questions our contact info:<br> Saúl López 011 +52 **************<br> Oscar Baez 011 +52 **************</h4>
								</td></tr>
							</table>
						</div>
					</td>
				</tr>
			</table>
			<table width='600'>
				<tr style='color: #fff;'>
					<td>
						<div style="background-image: url(<?php echo base_url('assets/img/mail/email-footer.jpg') ?>); height: 60px;"></div>
					</td>
				</tr>
			</table>
		</td></tr>
	</table>	
</body>
</html>