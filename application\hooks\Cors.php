<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * CORS Hook
 * 
 * This hook handles Cross-Origin Resource Sharing (CORS) headers
 * for all API requests to allow frontend applications to access the API.
 */
class Cors {
    
    /**
     * Handle CORS headers for all requests
     */
    public function handle_cors() {
        // Get CodeIgniter instance
        $CI =& get_instance();
        
        // Set CORS headers
        $CI->output->set_header('Access-Control-Allow-Origin: *');
        $CI->output->set_header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        $CI->output->set_header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin, X-API-Key');
        $CI->output->set_header('Access-Control-Allow-Credentials: true');
        $CI->output->set_header('Access-Control-Max-Age: 86400');
        
        // Handle preflight OPTIONS request
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            $CI->output->set_status_header(200);
            $CI->output->set_output('');
            $CI->output->_display();
            exit;
        }
    }
    
    /**
     * Handle CORS specifically for API routes
     */
    public function handle_api_cors() {
        // Get CodeIgniter instance
        $CI =& get_instance();
        
        // Check if this is an API request
        $uri = $CI->uri->uri_string();
        
        if (strpos($uri, 'api/') === 0) {
            $this->handle_cors();
        }
    }
}
