<?php

defined('BASEPATH') OR exit('No direct script access allowed');
class api extends CI_Controller {
    public function __construct()
    {
        parent::__construct();
        $this->load->model('api_model');
        // Asegurar instancia de DB y evitar HTML fatales del driver
        $this->load->database();
        if (isset($this->db)) {
            $this->db->db_debug = false; // manejaremos errores como JSON
        }
        
        // Habilitar CORS solo para rutas api/v1
        $this->_enable_cors();
    }

    private function _enable_cors()
    {
        // Limpiar headers previos para evitar duplicados
        if (function_exists('header_remove')) {
            header_remove('Access-Control-Allow-Origin');
            header_remove('Access-Control-Allow-Methods');
            header_remove('Access-Control-Allow-Headers');
            header_remove('Access-Control-Allow-Credentials');
            header_remove('Access-Control-Max-Age');
        }
        
        // Obtener el origen de la solicitud
        $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
        
        // Log para debugging
        log_message('debug', 'CORS Origin received: ' . $origin);
        
        // Lista de dominios permitidos expandida
        $allowed_origins = array(
            'http://localhost:3000',
            'http://localhost:5173',
            'http://localhost:8080',
            'http://localhost:4200',
            'http://localhost:5000',
            'https://localhost:3000',
            'https://localhost:5173',
            'https://localhost:8080',
            'https://localhost:4200',
            'https://localhost:5000',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:5173',
            'http://127.0.0.1:8080',
            'http://127.0.0.1:4200',
            'http://127.0.0.1:5000',
            'https://127.0.0.1:3000',
            'https://127.0.0.1:5173',
            'https://127.0.0.1:8080',
            'https://127.0.0.1:4200',
            'https://127.0.0.1:5000'
        );
        
        // Lógica de CORS más permisiva para desarrollo
        // $allowed_origin = '';
        
        if (!empty($origin)) {
            // Si el origen está en la lista explícita, úsalo
            if (in_array($origin, $allowed_origins)) {
                $allowed_origin = $origin;
                log_message('debug', 'CORS: Origin found in whitelist: ' . $origin);
            }
            // Si contiene localhost o 127.0.0.1, permitir automáticamente
            elseif (strpos($origin, 'localhost') !== false || strpos($origin, '127.0.0.1') !== false) {
                $allowed_origin = $origin;
                log_message('debug', 'CORS: Localhost origin allowed: ' . $origin);
            }
            // Para cualquier otro caso en desarrollo, permite el origen
            else {
                $allowed_origin = $origin;
                log_message('debug', 'CORS: Generic origin allowed: ' . $origin);
            }
        }
        if (empty($allowed_origin)) {
           $allowed_origin = '*';
        }
        
        // Establecer headers CORS
        header("Access-Control-Allow-Origin: " . $allowed_origin);
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE, PATCH");
        header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin, Cache-Control, Pragma, X-HTTP-Method-Override");
        header("Access-Control-Allow-Credentials: true");
        header("Access-Control-Max-Age: 86400");
        
        // Log headers para debugging
        log_message('debug', 'CORS headers set for origin: ' . $allowed_origin);
        
        // Manejar requests OPTIONS (preflight)
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            // Headers adicionales para preflight
            header("Access-Control-Expose-Headers: Content-Length, Content-Type");
            header("Content-Length: 0");
            http_response_code(200);
            exit();
        }
        
        // No establecer Content-Type aquí para evitar conflictos
        // Cada método lo establecerá según necesite
    }
    
    // --- Utilidades de respuesta y salud de DB ---
    private function respond_json($data, $status = 200, $headers = array())
    {
        foreach ($headers as $k => $v) {
            header($k . ': ' . $v);
        }
        $this->output->set_content_type('application/json');
        $this->output->set_status_header($status);
        $this->output->set_output(json_encode($data));
    }

    private function is_db_available()
    {
        try {
            if (!isset($this->db)) { return false; }
            if (!$this->db->conn_id) { $this->db->initialize(); }
            return (bool)$this->db->conn_id;
        } catch (Exception $e) {
            log_message('error', 'DB availability check failed: ' . $e->getMessage());
            return false;
        }
    }

    private function ensure_db_or_service_unavailable()
    {
        if (!$this->is_db_available()) {
            $this->respond_json(
                array(
                    'error' => 'Database service unavailable',
                    'code'  => 'DB_UNAVAILABLE'
                ),
                503,
                array('Retry-After' => '30')
            );
            return false;
        }
        return true;
    }

    // api/v1/health - Healthcheck ligero que NO depende de la DB para responder 200
    public function health()
    {
        $db_ok = $this->is_db_available();
        $payload = array(
            'service' => 'api/v1',
            'status'  => 'ok',
            'time'    => date('c'),
            'dependencies' => array(
                'database' => array(
                    'ok' => $db_ok,
                    'message' => $db_ok ? 'Database reachable' : 'Database unavailable'
                )
            )
        );
        $this->respond_json($payload, 200);
    }
    
    // Test endpoint
    public function test() {
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode(array('status' => 'api/v1 Controller is working', 'timestamp' => date('Y-m-d H:i:s'))));
    }
    
    // CORS Debug endpoint
    public function cors_test() {
        // Información detallada para debug de CORS
        $cors_info = array(
            'status' => 'CORS test endpoint working',
            'timestamp' => date('Y-m-d H:i:s'),
            'request_method' => $this->input->method(TRUE),
            'origin' => $this->input->get_request_header('Origin'),
            'user_agent' => $this->input->get_request_header('User-Agent'),
            'referer' => $this->input->get_request_header('Referer'),
            'content_type' => $this->input->get_request_header('Content-Type'),
            'all_headers' => $this->input->request_headers(),
            'server_info' => array(
                'php_version' => phpversion(),
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown'
            )
        );
        
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($cors_info, JSON_PRETTY_PRINT));
    }
    
    // endpoints

    // api/v1/airlines
    public function get_airlines() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $airlines = $this->api_model->get_airlines();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($airlines));
    }

    // api/v1/airports
    public function get_airports() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $airports = $this->api_model->get_airports();        
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($airports));
    }

    // api/v1/zones
    public function get_zones() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $zones = $this->api_model->get_zones();
        $hotelDB = $this->api_model->get_all_hotels();
        $jsonArray = array(
            'zones' => $zones,
            'hotelDB' => $hotelDB
        );
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($jsonArray));
    }
    // api/v1/get-fleet-services
    public function get_fleet_services() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $fleets = $this->api_model->get_all_service_fleets();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($fleets));
    }
    
    // api/v1/get-shared-rates
    public function share_rates() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $shared_rates = $this->api_model->get_shared_rates();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($shared_rates));
    }
    
    // api/v1/get-zones-with-count
    public function get_zones_with_count() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $zones = $this->api_model->get_zones_with_hotel_count();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($zones));
    }
    
    // api/v1/get-hotels-by-zone/{zone_id}
    public function get_hotels_by_zone($zone_id = null) {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        if ($zone_id === null) {
            $response = array('error' => 'Zone ID is required');
            $this->output->set_status_header(400);
        } else {
            $hotels = $this->api_model->get_hotels_by_zone($zone_id);
            $response = $hotels;
        }
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response));
    }
    
    // api/v1/fleets/detailed
    public function get_fleet_services_detailed() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $fleets = $this->api_model->get_fleet_services_detailed();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($fleets));
    }
    
    // api/v1/stats
    public function stats() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
    $stats = $this->api_model->get_api_stats();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($stats));
    }
    
    // api/v1/get-rates
    public function get_rates() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $rates = $this->api_model->get_all_rates();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($rates));
    }
    
    // api/v1/get-rates-detailed
    public function get_rates_detailed() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $rates = $this->api_model->get_rates_detailed();
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($rates));
    }
    
    // api/v1/get-rates-by-zone/{zone_id}
    public function get_rates_by_zone($zone_id = null) {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        if ($zone_id === null) {
            $response = array('error' => 'Zone ID is required');
            $this->output->set_status_header(400);
        } else {
            $rates = $this->api_model->get_rates_by_zone($zone_id);
            $response = $rates;
        }
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response));
    }

    // api/v1/get-grouped-rates-by-zone/{zone_id}
    public function get_grouped_rates_by_zone($zone_id = null) {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        if ($zone_id === null) {
            $response = array('error' => 'Zone ID is required');
            $this->output->set_status_header(400);
        } else {
            $grouped = $this->api_model->get_grouped_rates_by_zone($zone_id);
            $response = $grouped;
        }
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response));
    }
    
    // api/v1/get-rates-by-fleet/{fleet_id}
    public function get_rates_by_fleet($fleet_id = null) {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        if ($fleet_id === null) {
            $response = array('error' => 'Fleet ID is required');
            $this->output->set_status_header(400);
        } else {
            $rates = $this->api_model->get_rates_by_fleet($fleet_id);
            $response = $rates;
        }
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response));
    }

    // Nuevo endpoint para obtener zona por ID
    public function get_zone_by_id($zone_id = null) {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        if ($zone_id === null) {
            $response = array('error' => 'Zone ID is required');
            $this->output->set_status_header(400);
        } else {
            $zone = $this->api_model->get_zone_by_id($zone_id);
            $response = $zone ? $zone : array('error' => 'Zone not found');
        }
        
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response));
    }

    // Nuevo endpoint para zonas populares
    public function get_popular_zones($limit = 5) {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        $zones = $this->api_model->get_popular_zones($limit);
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($zones));
    }

    /**
     * Get fleets available for specific zone
     * @param int $zone_id
     */
    public function get_fleets_by_zone($zone_id = null) {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        // Validar que se proporcione zone_id
        if ($zone_id === null || !is_numeric($zone_id)) {
            $response = array(
                'error' => 'Zone ID is required and must be numeric',
                'code' => 'INVALID_ZONE_ID'
            );
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
            return;
        }
        
        try {
            $fleets = $this->api_model->get_fleets_by_zone($zone_id);
            
            // Manejar diferentes casos de respuesta
            if ($fleets === false) {
                // Zona no existe
                $response = array(
                    'error' => 'Zone not found',
                    'code' => 'ZONE_NOT_FOUND',
                    'zone_id' => (int)$zone_id
                );
                $this->output->set_status_header(404);
                
            } elseif ($fleets === null) {
                // Zona existe pero no tiene flotas disponibles
                $response = array(
                    'error' => 'No fleets available for this zone',
                    'code' => 'NO_FLEETS_AVAILABLE',
                    'zone_id' => (int)$zone_id,
                    'message' => 'The zone exists but no active fleets are configured for this destination'
                );
                $this->output->set_status_header(404);
                
            } elseif (is_array($fleets) && empty($fleets)) {
                // Error de base de datos
                $response = array(
                    'error' => 'Database error occurred',
                    'code' => 'DATABASE_ERROR'
                );
                $this->output->set_status_header(500);
                
            } else {
                // Éxito - flotas encontradas
                $response = array(
                    'success' => true,
                    'zone_id' => (int)$zone_id,
                    'total_fleets' => count($fleets),
                    'fleets' => $fleets
                );
                $this->output->set_status_header(200);
            }
            
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
            
        } catch (Exception $e) {
            $response = array(
                'error' => 'Server error occurred',
                'code' => 'INTERNAL_ERROR',
                'message' => 'Please try again later'
            );
            $this->output->set_status_header(500);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
            
            log_message('error', 'Exception in get_fleets_by_zone controller: ' . $e->getMessage());
        }
    }

    // api/v1/hotels - Obtener todos los hoteles O búsqueda filtrada
    public function get_all_hotels() {
    if (!$this->ensure_db_or_service_unavailable()) { return; }
        try {
            // Obtener parámetros de búsqueda de GET
            $search = $this->input->get('search');
            $zone_id = $this->input->get('zone_id');
            $limit = $this->input->get('limit');
            $page = $this->input->get('page');
            $sort_by = $this->input->get('sort_by');
            $sort_order = $this->input->get('sort_order');
            $status = $this->input->get('status');
            
            // Verificar si hay algún parámetro de búsqueda
            $has_search_params = !empty($search) || !empty($zone_id) || !empty($status);
            
            if ($has_search_params) {
                // MODO BÚSQUEDA: Usar búsqueda filtrada
                $params = array(
                    'search' => $search,
                    'zone_id' => $zone_id,
                    'limit' => $limit ? (int)$limit : null,
                    'page' => $page ? (int)$page : null,
                    'sort_by' => $sort_by,
                    'sort_order' => $sort_order,
                    'status' => $status
                );
                
                // Log de parámetros para debugging
                log_message('info', 'Hotels search request: ' . json_encode($params));
                
                $hotels = $this->api_model->search_hotels($params);
                $search_applied = true;
                
            } else {
                // MODO NORMAL: Obtener todos los hoteles (comportamiento original)
                $hotels = $this->api_model->get_all_hotels();
                $search_applied = false;
                $params = array();
                
                // Aplicar ordenamiento si se especifica (sin búsqueda)
                if (!empty($sort_by) || !empty($sort_order)) {
                    $params['sort_by'] = $sort_by;
                    $params['sort_order'] = $sort_order;
                    $hotels = $this->api_model->search_hotels($params);
                    $search_applied = true;
                }
            }
            
            if ($hotels === false) {
                // Error en la base de datos
                $response = array(
                    'error' => 'Database error occurred while fetching hotels',
                    'code' => 'DATABASE_ERROR'
                );
                $this->output->set_status_header(500);
                
            } else if (empty($hotels)) {
                // No hay hoteles encontrados
                $response = array(
                    'success' => true,
                    'total_hotels' => 0,
                    'hotels' => array(),
                    'message' => $search_applied ? 
                        ($search ? "No hotels found matching search term: '$search'" : 'No hotels found with specified filters') : 
                        'No hotels found in database',
                    'search_applied' => $search_applied,
                    'search_params' => $params
                );
                $this->output->set_status_header(200);
                
            } else {
                // Éxito - hoteles encontrados
                $response = array(
                    'success' => true,
                    'total_hotels' => count($hotels),
                    'hotels' => $hotels,
                    'search_applied' => $search_applied,
                    'search_params' => $params
                );
                $this->output->set_status_header(200);
            }
            
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
            
        } catch (Exception $e) {
            $response = array(
                'error' => 'Server error occurred',
                'code' => 'INTERNAL_ERROR',
                'message' => 'Please try again later'
            );
            $this->output->set_status_header(500);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
            
            log_message('error', 'Exception in get_all_hotels controller: ' . $e->getMessage());
        }
    }

    /**
     * Endpoint to save transportation booking data
     * @return void
     */
    public function save_trans_booking() {
        if (!$this->ensure_db_or_service_unavailable()) { return; }
        
        // Ensure that the request method is POST
        if ($this->input->method() !== 'post') {
            $response = array('error' => 'Method not allowed', 'code' => 'METHOD_NOT_ALLOWED');
            $this->output->set_status_header(405);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
            return;
        }

        // Get the JSON payload from the request body
        $booking_data = json_decode($this->input->raw_input_stream, true);

        // Log the received data for debugging
        log_message('info', 'Received booking data: ' . json_encode($booking_data));

        // Check if the booking data is valid
        if (empty($booking_data)) {
            log_message('error', 'Empty booking data received');
            $response = array('error' => 'Invalid booking data', 'code' => 'INVALID_BOOKING_DATA');
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
            return;
        }

        // Validate required fields
        $required_fields = ['transaction_id', 'firstName', 'lastName', 'email', 'phone_pax_full', 'subtotal', 'payment_method'];
        foreach ($required_fields as $field) {
            if (empty($booking_data[$field])) {
                log_message('error', "Missing required field: {$field}");
                $response = array('error' => "Missing required field: {$field}", 'code' => 'MISSING_REQUIRED_FIELD');
                $this->output->set_status_header(400);
                $this->output->set_content_type('application/json');
                $this->output->set_output(json_encode($response));
                return;
            }
        }

        // Save the transportation booking data using the Api_model
        $booking_id = $this->api_model->save_trans_booking($booking_data);

        if ($booking_id) {
            $response = array('success' => true, 'booking_id' => $booking_id, 'message' => 'Booking saved successfully');
            $this->output->set_status_header(201); // 201 Created
            log_message('info', 'Booking saved successfully with ID: ' . $booking_id);
        } else {
            $response = array('error' => 'Failed to save booking', 'code' => 'FAILED_TO_SAVE_BOOKING');
            $this->output->set_status_header(500); // 500 Internal Server Error
            log_message('error', 'Failed to save booking to database');
        }
        
        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response));
    }
    public function get_retail_by_booking_id($booking_id = null) {
        if (!$this->ensure_db_or_service_unavailable()) { return; }
        try {
            if ($booking_id === null) {
                $response = array('error' => 'Booking ID is required');
                $this->output->set_status_header(400);
            } else {
                $retail = $this->api_model->get_retail_booking_by_id($booking_id);
                $response = $retail;
            }
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
        } catch (\Throwable $th) {
            $response = array('error' => 'Failed to get booking', 'code' => 'FAILED_TO_GET_BOOKING');
            $this->output->set_status_header(500); // 500 Internal Server Error
            $this->output->set_content_type('application/json');
            $this->output->set_output(json_encode($response));
        }
    }
    public function set_email_send($booking_id = null) {
        if (!$this->ensure_db_or_service_unavailable()) { return; }

        try {
            // Validate booking ID
            if ($booking_id === null) {
                $response = array('error' => 'Booking ID is required', 'code' => 'MISSING_BOOKING_ID');
                $this->output->set_status_header(400); // 400 Bad Request
                $this->output->set_content_type('application/json');
                $this->output->set_output(json_encode($response));
                return;
            }

            // Get retail data directly from model (avoid calling public endpoint)
            $retail_data = $this->api_model->get_retail_booking_by_id($booking_id);

            // Validate if retail data exists
            if (empty($retail_data) || !isset($retail_data->email)) {
                $response = array('error' => 'Booking not found or invalid data', 'code' => 'BOOKING_NOT_FOUND');
                $this->output->set_status_header(404); // 404 Not Found
                $this->output->set_content_type('application/json');
                $this->output->set_output(json_encode($response));
                return;
            }

            // Load transportation configuration (contains email settings)
            $this->config->load('transportation');
            $custom_book = $this->config->item('custom_book');

            // Prepare email configuration
            $emailConfig = ENVIRONMENT === 'production' ?
                $this->config->item('smtp_reservationsBTT') :
                $this->config->item('smtp_mailtrapSandbox');

            if (empty($emailConfig)) {
                $config_name = ENVIRONMENT === 'production' ? 'smtp_reservationsBTT' : 'smtp_mailtrapSandbox';
                throw new Exception("Email configuration '$config_name' not found in transportation.php");
            }

            if (empty($custom_book)) {
                throw new Exception('Custom booking configuration not found in transportation.php');
            }

            // Prepare email data
            $subject = 'Baja Travel New Transportation Booking - Confirmation #' . $booking_id;
            $recipient_email = (ENVIRONMENT === 'production') ? $retail_data->email : '<EMAIL>';

            // Prepare data for email template
            $email_data = $this->prepare_email_data($retail_data);

            // Generate email message content using existing view
            $email_message = $this->load->view('templates/front-end/en/mail/booking-confirmation', $email_data, true);

            // Initialize and configure email (similar to Transportation controller)
            $this->email->initialize($emailConfig);

            $this->email->from('<EMAIL>', 'Baja Travel');
            $this->email->to($recipient_email);
            $this->email->subject($subject);
            $this->email->message($email_message);

            // Attach procedures file and set BCC based on destination (arrival_to)
            $this->attach_procedures_by_destination($retail_data);

            // Send email
            if ($this->email->send()) {
                $response = array(
                    'success' => true,
                    'message' => 'Email sent successfully',
                    'booking_id' => $booking_id,
                    'recipient' => $recipient_email,
                    'environment' => ENVIRONMENT
                );
                $this->output->set_status_header(200); // 200 OK
            } else {
                // Get detailed error information
                $email_debug = $this->email->print_debugger();

                // Log the error for debugging
                log_message('error', 'Email send failed for booking ' . $booking_id . ': ' . $email_debug);

                throw new Exception('Failed to send email: ' . $email_debug);
            }

        } catch (\Throwable $th) {
            log_message('error', 'Email send error for booking ' . $booking_id . ': ' . $th->getMessage());
            $response = array(
                'error' => 'Failed to send email',
                'code' => 'EMAIL_SEND_FAILED',
                'message' => ENVIRONMENT === 'development' ? $th->getMessage() : 'Internal server error'
            );
            $this->output->set_status_header(500); // 500 Internal Server Error
        }

        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response));
    }

    /**
     * Prepare data for email template
     * @param object $retail_data
     * @return array
     */
    private function prepare_email_data($retail_data) {
        // Determine if it's a roundtrip based on departure data
        $is_roundtrip = !empty($retail_data->departure_from) && !empty($retail_data->departure_to);

        // Map retail data to the format expected by the email template
        $transp_ret = array(
            'transaction_id' => isset($retail_data->booking_id) ? $retail_data->booking_id : 'N/A',
            'firstName' => isset($retail_data->name) ? $retail_data->name : '',
            'lastName' => isset($retail_data->lastname) ? $retail_data->lastname : '',
            'email' => isset($retail_data->email) ? $retail_data->email : '',
            'phone_pax_full' => isset($retail_data->phone) ? $retail_data->phone : '',
            'mobilePhone' => isset($retail_data->phone) ? $retail_data->phone : '',
            'payment_method' => isset($retail_data->payment_method) ? $retail_data->payment_method : 'N/A',
            'subtotal' => isset($retail_data->total_fare) ? $retail_data->total_fare : 0,
            'adults' => isset($retail_data->adults) ? $retail_data->adults : 1,
            'kids' => isset($retail_data->kids) ? $retail_data->kids : 0,
            'roundtrip' => $is_roundtrip,

            // Service information
            'fleet' => array(
                'name' => isset($retail_data->service) ? $retail_data->service : 'Transportation Service',
                'imgurl' => $this->get_fleet_image($retail_data->type_service ?? 'P'),
                'service' => isset($retail_data->type_service) ? $retail_data->type_service : 'P'
            ),

            // Trip details - Arrival
            'from' => isset($retail_data->arrival_from) ? $retail_data->arrival_from : 'Pickup Location',
            'to' => isset($retail_data->arrival_to) ? $retail_data->arrival_to : 'Destination',
            'fromsl' => 'Airport', // Default for arrival
            'pickupString' => 'Arrival',

            // Flight information - Arrival
            'arrivalAirline' => isset($retail_data->arrival_airline) ? $retail_data->arrival_airline : 'N/A',
            'arrivalFlight' => isset($retail_data->arrival_flight) ? $retail_data->arrival_flight : 'N/A',
            'arrivalTime' => isset($retail_data->arrival_time) ? $retail_data->arrival_time : 'N/A',
            'arrivalDate' => isset($retail_data->arrival_date) ? $retail_data->arrival_date : 'N/A',
            'arrivalPickup' => isset($retail_data->arrival_time) ? $retail_data->arrival_time : 'N/A',

            // Return trip information (if roundtrip)
            'fromdep' => isset($retail_data->departure_from) ? $retail_data->departure_from : '',
            'todep' => isset($retail_data->departure_to) ? $retail_data->departure_to : '',
            'fromdsl' => 'Hotel', // Default for departure
            'pickupRTString' => 'Departure',
            'departureAirline' => isset($retail_data->departure_airline) ? $retail_data->departure_airline : '',
            'departureFlight' => isset($retail_data->departure_flight) ? $retail_data->departure_flight : '',
            'departureTime' => isset($retail_data->departure_time) ? $retail_data->departure_time : '',
            'departureDate' => isset($retail_data->departure_date) ? $retail_data->departure_date : '',
            'departurePickup' => isset($retail_data->departure_pickup) ? $retail_data->departure_pickup : '',

            // Additional services (based on actual DB fields)
            'babySeats' => isset($retail_data->baby_seats) && $retail_data->baby_seats > 0 ? $retail_data->baby_seats : '',
            'carSeats' => isset($retail_data->car_seats) && $retail_data->car_seats > 0 ? $retail_data->car_seats : '',
            'boosterSeats' => isset($retail_data->booster_seats) && $retail_data->booster_seats > 0 ? $retail_data->booster_seats : '',

            // Grocery stop
            'add_grocery_stop' => isset($retail_data->grocery_stop_subtotal) && $retail_data->grocery_stop_subtotal > 0,
            'grocery_stop' => isset($retail_data->grocery_stop_subtotal) ? $retail_data->grocery_stop_subtotal : 0,

            // Golf clubs
            'add_golf_clubs_bags' => isset($retail_data->golf_clubs_bags_qty) && $retail_data->golf_clubs_bags_qty > 0,
            'golfClubsBags' => isset($retail_data->golf_clubs_bags_qty) ? $retail_data->golf_clubs_bags_qty : 0,
            'golf_clubs_bags' => isset($retail_data->golf_clubs_bags_price) ? $retail_data->golf_clubs_bags_price : 0,

            // Surfboards
            'add_surfboards' => isset($retail_data->surfboards_qty) && $retail_data->surfboards_qty > 0,
            'surfBoards' => isset($retail_data->surfboards_qty) ? $retail_data->surfboards_qty : 0,
            'surfboards' => isset($retail_data->surfboards_price) ? $retail_data->surfboards_price : 0,

            // Special instructions
            'specialInstructions' => isset($retail_data->comments) ? $retail_data->comments : ''
        );

        return array('transp_ret' => $transp_ret);
    }

    /**
     * Get fleet image based on service type
     * @param string $type_service
     * @return string
     */
    private function get_fleet_image($type_service) {
        switch ($type_service) {
            case 'S': // Shared
                return 'van-shuttle.png';
            case 'P': // Private
                return 'van-private.png';
            default:
                return 'van-private.png';
        }
    }

    /**
     * Attach procedures PDF based on destination (arrival_to)
     * @param object $retail_data
     * @return void
     */
    private function attach_procedures_by_destination($retail_data) {
        $custom_book = $this->config->item('custom_book');
        $arrival_to = isset($retail_data->arrival_to) ? $retail_data->arrival_to : '';

        // Default attachment
        $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures-btt.pdf';

        // Check specific destinations and attach corresponding PDF (based on Transportation controller logic)
        if ($arrival_to == 'Corazon Cabo Resort and SPA') {
            $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures.pdf';

            // Also update BCC for this destination
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed_cabo_villas'] : '<EMAIL>';
            $this->email->bcc($bcc_email);

        } elseif ($arrival_to == 'Medano Hotel and Suites') {
            $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures-medano.pdf';

            // Use default BCC for this destination
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed'] : '<EMAIL>';
            $this->email->bcc($bcc_email);

        } elseif ($arrival_to == 'ACRE Treehouses' || strpos($arrival_to, 'Acre') !== false || strpos($arrival_to, 'TreeHouse') !== false) {
            $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures-acre.pdf';

            // Use Acre-specific BCC
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed_acre'] : '<EMAIL>';
            $this->email->bcc($bcc_email);

        } else {
            // Default case - use default BCC
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed'] : '<EMAIL>';
            $this->email->bcc($bcc_email);
        }

        // Attach the file if it exists
        if ($this->url_exists($attachment_url)) {
            $this->email->attach($attachment_url);

            // Log which attachment was used (for debugging)
            log_message('info', "Email attachment for destination '$arrival_to': $attachment_url");
        } else {
            // Log if attachment file doesn't exist
            log_message('warning', "Email attachment file not found: $attachment_url for destination: $arrival_to");

            // Fallback to default attachment if specific one doesn't exist
            $fallback_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures-btt.pdf';
            if ($this->url_exists($fallback_url)) {
                $this->email->attach($fallback_url);
                log_message('info', "Using fallback attachment: $fallback_url");
            }
        }
    }

    /**
     * Check if URL exists
     * @param string $url
     * @return bool
     */
    private function url_exists($url) {
        $headers = @get_headers($url);
        return $headers && strpos($headers[0], '200') !== false;
    }

    /**
     * Debug endpoint to check email configuration (DEVELOPMENT ONLY)
     * Remove this method in production
     */
    public function debug_email_config() {
        if (ENVIRONMENT === 'production') {
            $this->output->set_status_header(404);
            return;
        }

        $this->config->load('transportation');
        $custom_book = $this->config->item('custom_book');
        $emailConfig = ENVIRONMENT === 'production' ?
            $this->config->item('smtp_reservationsBTT') :
            $this->config->item('smtp_mailtrapSandbox');

        // Check PHP mail configuration
        $php_mail_config = array(
            'sendmail_path' => ini_get('sendmail_path'),
            'SMTP' => ini_get('SMTP'),
            'smtp_port' => ini_get('smtp_port'),
            'sendmail_from' => ini_get('sendmail_from'),
            'mail.add_x_header' => ini_get('mail.add_x_header'),
            'mail_function_available' => function_exists('mail')
        );

        $debug_info = array(
            'environment' => ENVIRONMENT,
            'custom_book_loaded' => !empty($custom_book),
            'email_config_loaded' => !empty($emailConfig),
            'email_config' => $emailConfig,
            'custom_book_bcc' => isset($custom_book['bcc_book_payed']) ? $custom_book['bcc_book_payed'] : 'NOT_FOUND',
            'php_mail_config' => $php_mail_config,
            'openssl_loaded' => extension_loaded('openssl'),
            'curl_loaded' => extension_loaded('curl')
        );

        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($debug_info, JSON_PRETTY_PRINT));
    }

    /**
     * Test email endpoint (DEVELOPMENT ONLY)
     * Send a simple test email
     */
    public function test_email($recipient = null) {
        if (ENVIRONMENT === 'production') {
            $this->output->set_status_header(404);
            return;
        }

        try {
            $this->config->load('transportation');
            $emailConfig = $this->config->item('smtp_mailtrapSandbox');

            $test_recipient = $recipient ?: '<EMAIL>';

            $this->email->initialize($emailConfig);
            $this->email->clear();
            $this->email->from('<EMAIL>', 'Baja Travel Test');
            $this->email->to($test_recipient);
            $this->email->subject('Test Email - Baja Travel');
            $this->email->message('<h1>Test Email</h1><p>This is a test email from Baja Travel Transportation system.</p><p>Time: ' . date('Y-m-d H:i:s') . '</p>');

            if ($this->email->send()) {
                $response = array(
                    'success' => true,
                    'message' => 'Test email sent successfully',
                    'recipient' => $test_recipient,
                    'config_used' => $emailConfig
                );
            } else {
                $response = array(
                    'success' => false,
                    'message' => 'Failed to send test email',
                    'debug' => $this->email->print_debugger(),
                    'config_used' => $emailConfig
                );
            }

        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'message' => 'Exception occurred',
                'error' => $e->getMessage()
            );
        }

        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($response, JSON_PRETTY_PRINT));
    }

    /**
     * Debug endpoint to check which attachment would be used for a destination
     * Usage: /api/v1/debug-attachment/DESTINATION_NAME
     */
    public function debug_attachment($destination = null) {
        if (ENVIRONMENT === 'production') {
            $this->output->set_status_header(404);
            return;
        }

        $this->config->load('transportation');
        $custom_book = $this->config->item('custom_book');

        $test_destinations = array(
            'Corazon Cabo Resort and SPA',
            'Medano Hotel and Suites',
            'ACRE Treehouses',
            'Hotel Cabo San Lucas',
            'Default Destination'
        );

        if ($destination) {
            $test_destinations = array($destination);
        }

        $results = array();

        foreach ($test_destinations as $dest) {
            $attachment_info = $this->get_attachment_info_for_destination($dest, $custom_book);
            $results[$dest] = $attachment_info;
        }

        $this->output->set_content_type('application/json');
        $this->output->set_output(json_encode($results, JSON_PRETTY_PRINT));
    }

    /**
     * Get attachment info for a specific destination (for debugging)
     */
    private function get_attachment_info_for_destination($arrival_to, $custom_book) {
        $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures-btt.pdf';
        $bcc_email = '';

        if ($arrival_to == 'Corazon Cabo Resort and SPA') {
            $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures.pdf';
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed_cabo_villas'] : '<EMAIL>';

        } elseif ($arrival_to == 'Medano Hotel and Suites') {
            $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures-medano.pdf';
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed'] : '<EMAIL>';

        } elseif ($arrival_to == 'ACRE Treehouses' || strpos($arrival_to, 'Acre') !== false || strpos($arrival_to, 'TreeHouse') !== false) {
            $attachment_url = 'https://bajatraveltransportation.com/assets/procedures/custom-procedures-acre.pdf';
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed_acre'] : '<EMAIL>';

        } else {
            $bcc_email = (ENVIRONMENT === 'production') ? $custom_book['bcc_book_payed'] : '<EMAIL>';
        }

        return array(
            'destination' => $arrival_to,
            'attachment_url' => $attachment_url,
            'bcc_email' => $bcc_email,
            'file_exists' => $this->url_exists($attachment_url)
        );
    }
}
