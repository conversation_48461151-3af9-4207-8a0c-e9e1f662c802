# Configuración básica de reescritura de URLs
RewriteEngine on
RewriteCond $1 !^(index\.php|resources|robots\.txt)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L,QSA]

# Configuración básica para archivos estáticos
<Files "*.json">
    Header set Content-Type "application/json"
</Files>

# Configuración de charset por defecto
AddDefaultCharset UTF-8

# CORS Headers para API requests
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS, PUT, DELETE, PATCH"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin, Cache-Control, Pragma, X-API-Key"
Header always set Access-Control-Allow-Credentials "true"
Header always set Access-Control-Max-Age "86400"

# Manejar requests OPTIONS específicamente
<If "%{REQUEST_METHOD} == 'OPTIONS'">
    Header always set Content-Length "0"
    Header always set Content-Type "text/plain"
</If>