<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'home';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

/* ========================================
   API ROUTES - Para ReactJS
   ======================================== */

// API Base routes v1
$route['api/v1/test'] = 'api/test';
$route['api/v1/health'] = 'api/health';
$route['api/v1/stats'] = 'api/stats';

// Airlines API v1
$route['api/v1/airlines'] = 'api/get_airlines';

// Airports API v1
$route['api/v1/airports'] = 'api/get_airports';

// Zones API v1
$route['api/v1/zones'] = 'api/get_zones';
$route['api/v1/zones/popular/(:num)'] = 'api/get_popular_zones/$1';
$route['api/v1/zones/(:num)'] = 'api/get_zone_by_id/$1';

// Hotels API v1
$route['api/v1/hotels'] = 'api/get_all_hotels';
$route['api/v1/hotels/zone/(:num)'] = 'api/get_hotels_by_zone/$1';

// Rates API v1
$route['api/v1/rates'] = 'api/get_rates';
$route['api/v1/rates/detailed'] = 'api/get_rates_detailed';
$route['api/v1/rates/zone/(:num)'] = 'api/get_rates_by_zone/$1';
$route['api/v1/rates/grouped/zone/(:num)'] = 'api/get_grouped_rates_by_zone/$1'; // nuevas tarifas agrupadas (one_way + round_trip)
$route['api/v1/rates/fleet/(:num)'] = 'api/get_rates_by_fleet/$1';

// Fleet API v1
$route['api/v1/fleets'] = 'api/get_fleet_services';
$route['api/v1/fleets/detailed'] = 'api/get_fleet_services_detailed';
$route['api/v1/fleets/zone/(:num)'] = 'api/get_fleets_by_zone/$1';

// Save trans booking API v1
$route['api/v1/save-trans-booking'] = 'api/save_trans_booking';
//get retail by booking id API v1
$route['api/v1/get-retail-booking-id/(:any)'] = 'api/get_retail_by_booking_id/$1';

// Shared rates API v1
$route['api/v1/shared-rates'] = 'api/share_rates';

// Set email send API v1
$route['api/v1/set-email-send/(:any)'] = 'api/set_email_send/$1';

// Debug email config (development only)
$route['api/v1/debug-email-config'] = 'api/debug_email_config';

// Test email (development only)
$route['api/v1/test-email'] = 'api/test_email';
$route['api/v1/test-email/(:any)'] = 'api/test_email/$1';
/* ========================================
   MANTENER TODAS LAS RUTAS ACTUALES
   ======================================== */

/* TRANSPORTATION CONTROLLER */
$route['booking-service'] = 'transportation/booking_service';
$route['booking-details'] = 'transportation/booking_details';
$route['booking-confirmation'] = 'transportation/booking_confirmation';
$route['get-data'] = 'transportation/getData';
$route['init-booking'] = 'transportation/initBooking';
$route['save-previous-data'] = 'transportation/save_previous_data';
$route['record-currency'] = 'transportation/record_currency';
$route['get-basic-data'] = 'transportation/getBasicData';
$route['get-hotels-zones'] = 'transportation/getHotelsZones';
$route['get-hotels'] = 'transportation/getAllHotels';
$route['get-shared-rates'] = 'transportation/sharedRates';
$route['getZone'] = 'transportation/getZone';
$route['book-transfer1'] = 'transportation/book_transfer1';
$route['reservar-paso1'] = 'transportation/book_transfer1_es';
$route['pre-book-transfer2-usd'] = 'transportation/pre_book_transfer2_USD';
$route['book-transfer2-usd'] = 'transportation/book_transfer2_USD';
$route['update-retail-ids'] = 'transportation/update_retail_ids';
$route['special-payment/(:any)'] = 'transportation/special_payment/$1';
$route['pago-especial/(:any)'] = 'transportation/special_payment_es/$1';
// $route['booking-confirmation'] = 'transportation/booking_confirmation';
$route['confirmacion-de-reserva'] = 'transportation/booking_confirmation_es';
$route['get-data-bank'] = 'transportation/get_data_bank';
$route['book-transfer2-mxn'] = 'transportation/book_transfer2_MXN';
$route['payment-response'] = 'transportation/payment_response';
$route['confirmation'] = 'transportation/confirmation';

//Pruebas Quitar
// $route['get-one-activity'] = 'activities/get_one_activity';
// $route['set-act-retail-obj'] = 'activities/set_activity_retail_obj';
// $route['get-act-retail-obj'] = 'activities/get_activity_retail_obj';
$route['set-contact-info-obj'] = 'home/set_contact_info_obj';

$route['set-trans-retail-obj'] = 'transportation/set_trans_retail_obj';

// Templates Mail
$route['booking-confirmation-mail'] = 'home/booking_confirmation_mail';
$route['contact-confirmation'] = 'home/contact_confirmation_mail';

// Admin
$route['admin/get-bookings-for-table/(:num)'] = 'adminbken/get_bookings_for_table/$1';
$route['admin/get-bookings-for-table-by-user/(:any)/(:any)'] = 'adminbken/get_bookings_for_table_by_user/$1/$2';
$route['admin/booking/(:any)'] = 'adminbken/booking/$1';
$route['admin/(:any)'] = 'adminbken/page/$1';
$route['admin'] = 'adminbken/index';

$route['insert-user-db'] = 'users/insert_user_db';

// Enlaces especiales para hoteles
// $route['cabo-villas'] = 'home/cabo_villas'; //Checar este, no se utiliza
$route['booking-service/cabo-villas'] = 'transportation/booking_service';
$route['booking-service/corazon-cabo'] = 'transportation/booking_service';
$route['booking-service/medano-hotel'] = 'transportation/booking_service';
$route['booking-service/acre-treehouses'] = 'transportation/booking_service';

$route['booking-service/montemar'] = 'transportation/booking_service';
$route['booking-service/villa-nautilus'] = 'transportation/booking_service';
$route['booking-service/libertad-cabo'] = 'transportation/booking_service';
$route['booking-service/amaterra'] = 'transportation/booking_service';
$route['booking-service/bahia-tezal'] = 'transportation/booking_service';
$route['booking-service/vista-velas'] = 'transportation/booking_service';
$route['booking-service/tramonti'] = 'transportation/booking_service';
$route['booking-service/villa-paloma'] = 'transportation/booking_service';
$route['booking-service/villa-quetzal'] = 'transportation/booking_service';
$route['booking-service/villa-paradise'] = 'transportation/booking_service';
$route['booking-service/cabo-peninsula'] = 'transportation/booking_service';